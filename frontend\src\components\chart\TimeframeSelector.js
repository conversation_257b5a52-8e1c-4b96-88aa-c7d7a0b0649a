/**
 * 时间周期选择器组件
 * 负责K线图时间周期的切换功能
 */
class TimeframeSelector {
    constructor(container, options = {}) {
        this.container = container;
        this.options = {
            timeframes: ['1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w'],
            defaultTimeframe: '1h',
            position: 'top-left',
            theme: 'dark',
            ...options
        };
        
        this.activeTimeframe = this.options.defaultTimeframe;
        this.eventListeners = new Map();
        this.element = null;
        
        this.initialize();
    }

    /**
     * 初始化组件
     */
    initialize() {
        try {
            if (!this.container) {
                throw new Error('Container is required');
            }

            this.render();
            this.bindEvents();
            
            console.log('TimeframeSelector initialized successfully');
            this.emit('initialized');
            
        } catch (error) {
            console.error('Failed to initialize TimeframeSelector:', error);
            this.emit('error', error);
        }
    }

    /**
     * 渲染组件
     */
    render() {
        // 创建主容器
        this.element = document.createElement('div');
        this.element.className = `timeframe-selector ${this.options.theme}`;
        this.element.style.cssText = this.getContainerStyles();

        // 创建按钮组
        const buttonGroup = document.createElement('div');
        buttonGroup.className = 'timeframe-buttons';
        buttonGroup.style.cssText = this.getButtonGroupStyles();

        // 创建时间周期按钮
        this.options.timeframes.forEach(timeframe => {
            const button = this.createTimeframeButton(timeframe);
            buttonGroup.appendChild(button);
        });

        this.element.appendChild(buttonGroup);
        this.container.appendChild(this.element);

        // 设置初始激活状态
        this.setActive(this.activeTimeframe);
    }

    /**
     * 创建时间周期按钮
     * @param {string} timeframe - 时间周期
     * @returns {HTMLElement} 按钮元素
     */
    createTimeframeButton(timeframe) {
        const button = document.createElement('button');
        button.className = 'timeframe-button';
        button.dataset.timeframe = timeframe;
        button.textContent = timeframe.toUpperCase();
        button.style.cssText = this.getButtonStyles();
        
        // 添加悬停效果
        button.addEventListener('mouseenter', () => {
            if (!button.classList.contains('active')) {
                button.style.backgroundColor = this.getHoverColor();
            }
        });
        
        button.addEventListener('mouseleave', () => {
            if (!button.classList.contains('active')) {
                button.style.backgroundColor = 'transparent';
            }
        });

        return button;
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        if (!this.element) return;

        // 委托事件处理按钮点击
        this.element.addEventListener('click', (event) => {
            if (event.target.classList.contains('timeframe-button')) {
                const timeframe = event.target.dataset.timeframe;
                this.setActive(timeframe);
                this.emit('timeframeChange', timeframe);
            }
        });
    }

    /**
     * 设置激活的时间周期
     * @param {string} timeframe - 时间周期
     */
    setActive(timeframe) {
        if (!this.options.timeframes.includes(timeframe)) {
            console.warn(`Invalid timeframe: ${timeframe}`);
            return;
        }

        // 移除所有按钮的激活状态
        const buttons = this.element.querySelectorAll('.timeframe-button');
        buttons.forEach(button => {
            button.classList.remove('active');
            button.style.backgroundColor = 'transparent';
            button.style.color = this.getInactiveColor();
        });

        // 设置新的激活按钮
        const activeButton = this.element.querySelector(`[data-timeframe="${timeframe}"]`);
        if (activeButton) {
            activeButton.classList.add('active');
            activeButton.style.backgroundColor = this.getActiveColor();
            activeButton.style.color = '#ffffff';
        }

        this.activeTimeframe = timeframe;
        this.emit('activeChanged', timeframe);
    }

    /**
     * 获取当前激活的时间周期
     * @returns {string} 当前激活的时间周期
     */
    getActive() {
        return this.activeTimeframe;
    }

    /**
     * 获取容器样式
     * @returns {string} CSS样式字符串
     */
    getContainerStyles() {
        const position = this.getPositionStyles();
        return `
            position: absolute;
            ${position}
            z-index: 100;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 6px;
            padding: 8px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        `;
    }

    /**
     * 获取位置样式
     * @returns {string} 位置CSS样式
     */
    getPositionStyles() {
        switch (this.options.position) {
            case 'top-left':
                return 'top: 15px; left: 15px;';
            case 'top-right':
                return 'top: 15px; right: 15px;';
            case 'bottom-left':
                return 'bottom: 15px; left: 15px;';
            case 'bottom-right':
                return 'bottom: 15px; right: 15px;';
            default:
                return 'top: 15px; left: 15px;';
        }
    }

    /**
     * 获取按钮组样式
     * @returns {string} CSS样式字符串
     */
    getButtonGroupStyles() {
        return `
            display: flex;
            gap: 4px;
            align-items: center;
        `;
    }

    /**
     * 获取按钮样式
     * @returns {string} CSS样式字符串
     */
    getButtonStyles() {
        return `
            background: transparent;
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: ${this.getInactiveColor()};
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 36px;
            text-align: center;
            user-select: none;
        `;
    }

    /**
     * 获取激活颜色
     * @returns {string} 颜色值
     */
    getActiveColor() {
        return this.options.theme === 'dark' ? '#3498db' : '#2980b9';
    }

    /**
     * 获取非激活颜色
     * @returns {string} 颜色值
     */
    getInactiveColor() {
        return this.options.theme === 'dark' ? '#bdc3c7' : '#7f8c8d';
    }

    /**
     * 获取悬停颜色
     * @returns {string} 颜色值
     */
    getHoverColor() {
        return this.options.theme === 'dark' ? 'rgba(52, 152, 219, 0.3)' : 'rgba(41, 128, 185, 0.3)';
    }

    /**
     * 更新时间周期选项
     * @param {Array} timeframes - 新的时间周期数组
     */
    updateTimeframes(timeframes) {
        if (!Array.isArray(timeframes) || timeframes.length === 0) {
            console.warn('Invalid timeframes array');
            return;
        }

        this.options.timeframes = timeframes;
        
        // 如果当前激活的时间周期不在新列表中，设置为第一个
        if (!timeframes.includes(this.activeTimeframe)) {
            this.activeTimeframe = timeframes[0];
        }

        // 重新渲染
        if (this.element) {
            this.element.remove();
            this.render();
        }
    }

    /**
     * 显示组件
     */
    show() {
        if (this.element) {
            this.element.style.display = 'block';
        }
    }

    /**
     * 隐藏组件
     */
    hide() {
        if (this.element) {
            this.element.style.display = 'none';
        }
    }

    /**
     * 销毁组件
     */
    destroy() {
        try {
            if (this.element) {
                this.element.remove();
                this.element = null;
            }
            
            this.eventListeners.clear();
            this.emit('destroyed');
            
        } catch (error) {
            console.error('Failed to destroy TimeframeSelector:', error);
        }
    }

    /**
     * 事件监听
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    /**
     * 移除事件监听
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    off(event, callback) {
        if (this.eventListeners.has(event)) {
            const listeners = this.eventListeners.get(event);
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }

    /**
     * 触发事件
     * @param {string} event - 事件名称
     * @param {*} data - 事件数据
     */
    emit(event, data) {
        if (this.eventListeners.has(event)) {
            this.eventListeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in event listener for ${event}:`, error);
                }
            });
        }
    }
}

// 导出组件
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TimeframeSelector;
} else {
    window.TimeframeSelector = TimeframeSelector;
}
