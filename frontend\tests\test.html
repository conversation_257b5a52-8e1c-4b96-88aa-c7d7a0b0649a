<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CryptoTradingApp - 单元测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #1a1a1a;
            color: #ffffff;
            margin: 0;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            color: #3498db;
            text-align: center;
            margin-bottom: 30px;
        }

        .test-controls {
            background: #2c3e50;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 0 10px;
            transition: background-color 0.3s;
        }

        .btn:hover {
            background: #2980b9;
        }

        .btn.success {
            background: #27ae60;
        }

        .btn.success:hover {
            background: #229954;
        }

        .btn.danger {
            background: #e74c3c;
        }

        .btn.danger:hover {
            background: #c0392b;
        }

        .test-results {
            background: #2c3e50;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .test-output {
            background: #1a1a1a;
            border: 1px solid #34495e;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: #34495e;
            border-radius: 6px;
            min-width: 120px;
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            color: #bdc3c7;
            text-transform: uppercase;
        }

        .success { color: #27ae60; }
        .error { color: #e74c3c; }
        .warning { color: #f39c12; }
        .info { color: #3498db; }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #34495e;
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 20px;
        }

        .progress-fill {
            height: 100%;
            background: #3498db;
            width: 0%;
            transition: width 0.3s ease;
        }

        .test-item {
            padding: 8px 0;
            border-bottom: 1px solid #34495e;
        }

        .test-item:last-child {
            border-bottom: none;
        }

        .test-name {
            font-weight: bold;
        }

        .test-status {
            float: right;
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 3px;
        }

        .test-status.passed {
            background: #27ae60;
            color: white;
        }

        .test-status.failed {
            background: #e74c3c;
            color: white;
        }

        .test-status.running {
            background: #f39c12;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 CryptoTradingApp 单元测试</h1>
        
        <div class="test-controls">
            <button class="btn success" onclick="runAllTests()">🚀 运行所有测试</button>
            <button class="btn" onclick="runComponentTests()">🔧 组件测试</button>
            <button class="btn" onclick="runManagerTests()">📊 管理器测试</button>
            <button class="btn danger" onclick="clearOutput()">🗑️ 清除输出</button>
        </div>

        <div class="test-results">
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-value success" id="passed-count">0</div>
                    <div class="stat-label">通过</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value error" id="failed-count">0</div>
                    <div class="stat-label">失败</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value info" id="total-count">0</div>
                    <div class="stat-label">总计</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value warning" id="success-rate">0%</div>
                    <div class="stat-label">成功率</div>
                </div>
            </div>
            
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill"></div>
            </div>
            
            <div class="test-output" id="test-output">
                等待测试运行...
                
使用说明：
1. 点击"运行所有测试"执行完整的测试套件
2. 点击"组件测试"只运行组件相关测试
3. 点击"管理器测试"只运行数据管理器测试
4. 测试结果将显示在下方的输出区域

测试覆盖范围：
- ChartComponent: 图表组件功能测试
- MarketDataPanel: 市场数据面板测试
- TimeframeSelector: 时间周期选择器测试
- MarketDataManager: 数据管理器测试
- 性能优化工具测试
- 错误处理测试
            </div>
        </div>
    </div>

    <!-- 加载依赖 -->
    <script src="../src/utils/performance.js"></script>
    <script src="test-runner.js"></script>
    
    <!-- 加载组件 -->
    <script src="../src/components/chart/ChartComponent.js"></script>
    <script src="../src/components/chart/TimeframeSelector.js"></script>
    <script src="../src/components/market/MarketDataPanel.js"></script>
    <script src="../src/managers/MarketDataManager.js"></script>
    
    <!-- 加载测试 -->
    <script src="components/chart-component.test.js"></script>
    <script src="components/market-data-panel.test.js"></script>
    <script src="managers/market-data-manager.test.js"></script>

    <script>
        let currentTestRun = null;
        let testOutput = document.getElementById('test-output');
        let originalConsoleLog = console.log;
        let originalConsoleError = console.error;
        let originalConsoleWarn = console.warn;

        // 重定向控制台输出到测试输出区域
        function redirectConsole() {
            console.log = function(...args) {
                appendToOutput(args.join(' '), 'info');
                originalConsoleLog.apply(console, args);
            };

            console.error = function(...args) {
                appendToOutput(args.join(' '), 'error');
                originalConsoleError.apply(console, args);
            };

            console.warn = function(...args) {
                appendToOutput(args.join(' '), 'warning');
                originalConsoleWarn.apply(console, args);
            };
        }

        // 恢复控制台输出
        function restoreConsole() {
            console.log = originalConsoleLog;
            console.error = originalConsoleError;
            console.warn = originalConsoleWarn;
        }

        // 添加输出到测试区域
        function appendToOutput(text, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const line = `[${timestamp}] ${text}\n`;
            
            if (testOutput) {
                testOutput.textContent += line;
                testOutput.scrollTop = testOutput.scrollHeight;
            }
        }

        // 更新统计信息
        function updateStats(results) {
            document.getElementById('passed-count').textContent = results.passed;
            document.getElementById('failed-count').textContent = results.failed;
            document.getElementById('total-count').textContent = results.total;
            
            const successRate = results.total > 0 ? 
                ((results.passed / results.total) * 100).toFixed(1) : 0;
            document.getElementById('success-rate').textContent = successRate + '%';
            
            // 更新进度条
            const progressFill = document.getElementById('progress-fill');
            if (progressFill) {
                progressFill.style.width = '100%';
                progressFill.style.background = results.failed > 0 ? '#e74c3c' : '#27ae60';
            }
        }

        // 运行所有测试
        async function runAllTests() {
            clearOutput();
            redirectConsole();
            
            try {
                appendToOutput('🚀 开始运行所有测试...', 'info');
                
                const results = await TestUtils.testRunner.run();
                updateStats(results);
                
                if (results.failed === 0) {
                    appendToOutput('🎉 所有测试通过！', 'success');
                } else {
                    appendToOutput(`⚠️ ${results.failed} 个测试失败`, 'error');
                }
                
            } catch (error) {
                appendToOutput(`❌ 测试运行失败: ${error.message}`, 'error');
            } finally {
                restoreConsole();
            }
        }

        // 运行组件测试
        async function runComponentTests() {
            clearOutput();
            appendToOutput('🔧 运行组件测试...', 'info');
            
            // 这里可以添加只运行特定测试的逻辑
            await runAllTests();
        }

        // 运行管理器测试
        async function runManagerTests() {
            clearOutput();
            appendToOutput('📊 运行管理器测试...', 'info');
            
            // 这里可以添加只运行特定测试的逻辑
            await runAllTests();
        }

        // 清除输出
        function clearOutput() {
            if (testOutput) {
                testOutput.textContent = '';
            }
            
            // 重置统计
            updateStats({ passed: 0, failed: 0, total: 0 });
            
            // 重置进度条
            const progressFill = document.getElementById('progress-fill');
            if (progressFill) {
                progressFill.style.width = '0%';
                progressFill.style.background = '#3498db';
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            appendToOutput('✅ 测试环境已准备就绪', 'success');
            appendToOutput(`📋 已加载 ${TestUtils.testRunner.tests.length} 个测试`, 'info');
        });
    </script>
</body>
</html>
