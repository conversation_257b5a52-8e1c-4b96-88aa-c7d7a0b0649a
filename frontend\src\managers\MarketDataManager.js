/**
 * 市场数据管理器
 * 负责管理多个交易对的实时数据获取、缓存和状态管理
 */
class MarketDataManager {
    constructor(config = {}) {
        this.config = {
            apiBaseUrl: 'http://127.0.0.1:8001',
            wsBaseUrl: 'ws://127.0.0.1:8001',
            reconnectInterval: 5000,
            maxReconnectAttempts: 10,
            cacheTimeout: 60000, // 1分钟缓存
            ...config
        };
        
        this.connections = new Map(); // WebSocket连接池
        this.dataCache = new Map(); // 数据缓存
        this.subscribers = new Map(); // 事件订阅者
        this.reconnectAttempts = new Map(); // 重连尝试次数
        this.connectionStatus = new Map(); // 连接状态
        
        this.isInitialized = false;
        this.initialize();
    }

    /**
     * 初始化管理器
     */
    initialize() {
        try {
            console.log('MarketDataManager initializing...');
            this.isInitialized = true;
            this.emit('initialized');
        } catch (error) {
            console.error('Failed to initialize MarketDataManager:', error);
            this.emit('error', error);
        }
    }

    /**
     * 连接到指定交易对的实时数据流
     * @param {string} symbol - 交易对符号
     * @param {string} timeframe - 时间周期
     * @returns {Promise<boolean>} 连接是否成功
     */
    async connect(symbol, timeframe = '1h') {
        try {
            const key = `${symbol}_${timeframe}`;

            // 如果已经连接，直接返回
            if (this.connections.has(key) && this.getConnectionStatus(symbol, timeframe) === 'connected') {
                console.log(`Already connected to ${key}`);
                return true;
            }

            console.log(`Connecting to ${key}...`);

            // 创建WebSocket连接
            const wsUrl = `${this.config.wsBaseUrl}/ws/kline/${symbol}/${timeframe}`;

            // 检查WebSocket是否可用
            if (typeof WebSocket === 'undefined') {
                throw new Error('WebSocket is not supported in this environment');
            }

            const ws = new WebSocket(wsUrl);

            // 设置连接状态
            this.setConnectionStatus(symbol, timeframe, 'connecting');

            // 设置事件处理器
            this.setupWebSocketHandlers(ws, symbol, timeframe);

            // 存储连接
            this.connections.set(key, ws);

            return new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    this.setConnectionStatus(symbol, timeframe, 'timeout');
                    reject(new Error(`Connection timeout for ${key}`));
                }, 10000);

                ws.onopen = () => {
                    clearTimeout(timeout);
                    this.setConnectionStatus(symbol, timeframe, 'connected');
                    this.resetReconnectAttempts(symbol, timeframe);
                    console.log(`Connected to ${key}`);
                    this.emit('connected', { symbol, timeframe });
                    resolve(true);
                };

                ws.onerror = (error) => {
                    clearTimeout(timeout);
                    console.error(`Connection error for ${key}:`, error);
                    this.setConnectionStatus(symbol, timeframe, 'error');
                    this.emit('connectionError', { symbol, timeframe, error });
                    reject(new Error(`Failed to connect to ${key}: ${error.message || 'Unknown error'}`));
                };
            });

        } catch (error) {
            console.error(`Failed to connect to ${symbol}_${timeframe}:`, error);
            this.setConnectionStatus(symbol, timeframe, 'error');
            this.emit('error', error);
            return false;
        }
    }

    /**
     * 设置WebSocket事件处理器
     * @param {WebSocket} ws - WebSocket实例
     * @param {string} symbol - 交易对符号
     * @param {string} timeframe - 时间周期
     */
    setupWebSocketHandlers(ws, symbol, timeframe) {
        const key = `${symbol}_${timeframe}`;
        
        ws.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                
                // 处理心跳消息
                if (data.type === 'heartbeat') {
                    console.log(`Heartbeat received for ${key}`);
                    return;
                }
                
                // 处理K线数据
                if (data.data) {
                    this.handleKlineData(symbol, timeframe, data);
                }
                
            } catch (error) {
                console.error(`Failed to process message for ${key}:`, error);
            }
        };
        
        ws.onclose = (event) => {
            console.log(`Connection closed for ${key}:`, event.code, event.reason);
            this.setConnectionStatus(symbol, timeframe, 'disconnected');
            this.emit('disconnected', { symbol, timeframe, event });
            
            // 自动重连
            this.scheduleReconnect(symbol, timeframe);
        };
        
        ws.onerror = (error) => {
            console.error(`WebSocket error for ${key}:`, error);
            this.setConnectionStatus(symbol, timeframe, 'error');
            this.emit('connectionError', { symbol, timeframe, error });
        };
    }

    /**
     * 处理K线数据
     * @param {string} symbol - 交易对符号
     * @param {string} timeframe - 时间周期
     * @param {Object} data - 数据
     */
    handleKlineData(symbol, timeframe, data) {
        try {
            const key = `${symbol}_${timeframe}`;
            
            // 缓存数据
            this.setCachedData(symbol, timeframe, data.data);
            
            // 发布数据更新事件
            this.emit('dataUpdate', {
                symbol,
                timeframe,
                data: data.data,
                isClosed: data.is_closed
            });
            
            // 如果是主要交易对，发布市场数据更新
            if (['BTCUSDT', 'ETHUSDT', 'SOLUSDT'].includes(symbol)) {
                this.emit('marketDataUpdate', {
                    symbol,
                    price: parseFloat(data.data.close),
                    change: parseFloat(data.data.close) - parseFloat(data.data.open),
                    changePercent: ((parseFloat(data.data.close) - parseFloat(data.data.open)) / parseFloat(data.data.open)) * 100,
                    volume: parseFloat(data.data.volume),
                    high24h: parseFloat(data.data.high),
                    low24h: parseFloat(data.data.low),
                    timestamp: new Date(data.data.timestamp).getTime()
                });
            }
            
        } catch (error) {
            console.error('Failed to handle kline data:', error);
        }
    }

    /**
     * 断开连接
     * @param {string} symbol - 交易对符号
     * @param {string} timeframe - 时间周期
     */
    disconnect(symbol, timeframe = '1h') {
        try {
            const key = `${symbol}_${timeframe}`;
            const ws = this.connections.get(key);
            
            if (ws) {
                ws.close();
                this.connections.delete(key);
                this.setConnectionStatus(symbol, timeframe, 'disconnected');
                console.log(`Disconnected from ${key}`);
                this.emit('disconnected', { symbol, timeframe });
            }
            
        } catch (error) {
            console.error(`Failed to disconnect from ${symbol}_${timeframe}:`, error);
        }
    }

    /**
     * 重连到指定交易对
     * @param {string} symbol - 交易对符号
     * @param {string} timeframe - 时间周期
     */
    async reconnect(symbol, timeframe = '1h') {
        console.log(`Attempting to reconnect to ${symbol}_${timeframe}`);
        
        // 先断开现有连接
        this.disconnect(symbol, timeframe);
        
        // 等待一下再重连
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 重新连接
        return this.connect(symbol, timeframe);
    }

    /**
     * 安排重连
     * @param {string} symbol - 交易对符号
     * @param {string} timeframe - 时间周期
     */
    scheduleReconnect(symbol, timeframe) {
        const key = `${symbol}_${timeframe}`;
        const attempts = this.getReconnectAttempts(symbol, timeframe);
        
        if (attempts >= this.config.maxReconnectAttempts) {
            console.log(`Max reconnect attempts reached for ${key}`);
            this.emit('maxReconnectAttemptsReached', { symbol, timeframe });
            return;
        }
        
        const delay = this.config.reconnectInterval * Math.pow(2, attempts); // 指数退避
        console.log(`Scheduling reconnect for ${key} in ${delay}ms (attempt ${attempts + 1})`);
        
        setTimeout(async () => {
            this.incrementReconnectAttempts(symbol, timeframe);
            await this.reconnect(symbol, timeframe);
        }, delay);
    }

    /**
     * 获取历史数据
     * @param {string} symbol - 交易对符号
     * @param {string} timeframe - 时间周期
     * @param {number} limit - 数据条数
     * @returns {Promise<Array>} 历史数据
     */
    async getHistoricalData(symbol, timeframe = '1h', limit = 500) {
        try {
            const url = `${this.config.apiBaseUrl}/api/kline/${symbol}/${timeframe}?limit=${limit}`;
            const response = await fetch(url);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            
            if (!result.data || !Array.isArray(result.data)) {
                throw new Error('Invalid data format');
            }
            
            // 缓存数据
            this.setCachedData(symbol, timeframe, result.data);
            
            return result.data;
            
        } catch (error) {
            console.error(`Failed to get historical data for ${symbol}_${timeframe}:`, error);
            throw error;
        }
    }

    /**
     * 获取缓存数据
     * @param {string} symbol - 交易对符号
     * @param {string} timeframe - 时间周期
     * @returns {*} 缓存的数据
     */
    getCachedData(symbol, timeframe = '1h') {
        const key = `${symbol}_${timeframe}`;
        const cached = this.dataCache.get(key);
        
        if (!cached) return null;
        
        // 检查缓存是否过期
        if (Date.now() - cached.timestamp > this.config.cacheTimeout) {
            this.dataCache.delete(key);
            return null;
        }
        
        return cached.data;
    }

    /**
     * 设置缓存数据
     * @param {string} symbol - 交易对符号
     * @param {string} timeframe - 时间周期
     * @param {*} data - 数据
     */
    setCachedData(symbol, timeframe, data) {
        const key = `${symbol}_${timeframe}`;
        this.dataCache.set(key, {
            data,
            timestamp: Date.now()
        });
    }

    /**
     * 清除缓存
     * @param {string} symbol - 交易对符号（可选）
     * @param {string} timeframe - 时间周期（可选）
     */
    clearCache(symbol = null, timeframe = null) {
        if (symbol && timeframe) {
            const key = `${symbol}_${timeframe}`;
            this.dataCache.delete(key);
        } else if (symbol) {
            // 清除指定符号的所有缓存
            for (const key of this.dataCache.keys()) {
                if (key.startsWith(symbol)) {
                    this.dataCache.delete(key);
                }
            }
        } else {
            // 清除所有缓存
            this.dataCache.clear();
        }
    }

    /**
     * 获取连接状态
     * @param {string} symbol - 交易对符号
     * @param {string} timeframe - 时间周期
     * @returns {string} 连接状态
     */
    getConnectionStatus(symbol, timeframe = '1h') {
        const key = `${symbol}_${timeframe}`;
        return this.connectionStatus.get(key) || 'disconnected';
    }

    /**
     * 设置连接状态
     * @param {string} symbol - 交易对符号
     * @param {string} timeframe - 时间周期
     * @param {string} status - 状态
     */
    setConnectionStatus(symbol, timeframe, status) {
        const key = `${symbol}_${timeframe}`;
        this.connectionStatus.set(key, status);
        this.emit('statusChange', { symbol, timeframe, status });
    }

    /**
     * 获取重连尝试次数
     * @param {string} symbol - 交易对符号
     * @param {string} timeframe - 时间周期
     * @returns {number} 重连尝试次数
     */
    getReconnectAttempts(symbol, timeframe) {
        const key = `${symbol}_${timeframe}`;
        return this.reconnectAttempts.get(key) || 0;
    }

    /**
     * 增加重连尝试次数
     * @param {string} symbol - 交易对符号
     * @param {string} timeframe - 时间周期
     */
    incrementReconnectAttempts(symbol, timeframe) {
        const key = `${symbol}_${timeframe}`;
        const current = this.getReconnectAttempts(symbol, timeframe);
        this.reconnectAttempts.set(key, current + 1);
    }

    /**
     * 重置重连尝试次数
     * @param {string} symbol - 交易对符号
     * @param {string} timeframe - 时间周期
     */
    resetReconnectAttempts(symbol, timeframe) {
        const key = `${symbol}_${timeframe}`;
        this.reconnectAttempts.set(key, 0);
    }

    /**
     * 获取所有连接状态
     * @returns {Object} 连接状态对象
     */
    getAllConnectionStatus() {
        const status = {};
        for (const [key, value] of this.connectionStatus.entries()) {
            status[key] = value;
        }
        return status;
    }

    /**
     * 获取数据状态
     * @returns {Object} 数据状态对象
     */
    getDataStatus() {
        return {
            cacheSize: this.dataCache.size,
            connections: this.connections.size,
            connectionStatus: this.getAllConnectionStatus()
        };
    }

    /**
     * 订阅事件
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    subscribe(event, callback) {
        if (!this.subscribers.has(event)) {
            this.subscribers.set(event, []);
        }
        this.subscribers.get(event).push(callback);
    }

    /**
     * 取消订阅事件
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    unsubscribe(event, callback) {
        if (this.subscribers.has(event)) {
            const callbacks = this.subscribers.get(event);
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        }
    }

    /**
     * 发布事件
     * @param {string} event - 事件名称
     * @param {*} data - 事件数据
     */
    emit(event, data) {
        if (this.subscribers.has(event)) {
            this.subscribers.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in event subscriber for ${event}:`, error);
                }
            });
        }
    }

    /**
     * 销毁管理器
     */
    destroy() {
        try {
            // 关闭所有连接
            for (const [key, ws] of this.connections.entries()) {
                ws.close();
            }
            
            // 清理数据
            this.connections.clear();
            this.dataCache.clear();
            this.subscribers.clear();
            this.reconnectAttempts.clear();
            this.connectionStatus.clear();
            
            this.isInitialized = false;
            console.log('MarketDataManager destroyed');
            
        } catch (error) {
            console.error('Failed to destroy MarketDataManager:', error);
        }
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MarketDataManager;
} else {
    window.MarketDataManager = MarketDataManager;
}
