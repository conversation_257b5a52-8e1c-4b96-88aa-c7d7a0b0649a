/**
 * 主应用控制器
 * 负责协调所有组件和管理应用状态
 */
class CryptoTradingApp {
    constructor() {
        this.config = {
            defaultSymbol: 'BTCUSDT',
            defaultTimeframe: '1h',
            symbols: ['BTCUSDT', 'ETHUSDT', 'SOLUSDT'],
            timeframes: ['1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w']
        };
        
        this.state = {
            currentSymbol: this.config.defaultSymbol,
            currentTimeframe: this.config.defaultTimeframe,
            isInitialized: false,
            isLoading: false,
            error: null
        };
        
        // 组件实例
        this.chartComponent = null;
        this.timeframeSelector = null;
        this.marketDataPanel = null;
        this.technicalIndicators = null;
        
        // 管理器实例
        this.marketDataManager = null;

        // 性能优化实例
        this.performanceMonitor = window.PerformanceUtils?.performanceMonitor;
        this.memoryMonitor = window.PerformanceUtils?.memoryMonitor;
        this.errorBoundary = window.PerformanceUtils?.errorBoundary;
        this.resourceCleaner = window.PerformanceUtils?.resourceCleaner;

        // 事件监听器
        this.eventListeners = new Map();

        // 防抖函数
        this.debouncedResize = window.PerformanceUtils?.debounce(() => {
            this.handleResize();
        }, 250);

        this.throttledDataUpdate = window.PerformanceUtils?.throttle((data) => {
            this.handleDataUpdate(data);
        }, 100);
        
        this.initialize();
    }

    /**
     * 初始化应用
     */
    async initialize() {
        try {
            console.log('Initializing CryptoTradingApp...');

            // 开始性能监控
            if (this.performanceMonitor) {
                this.performanceMonitor.mark('app-init-start');
            }

            // 启动内存监控
            if (this.memoryMonitor) {
                this.memoryMonitor.start();
                this.memoryMonitor.onMemoryCheck((info) => {
                    if (parseFloat(info.usagePercent) > 80) {
                        console.warn('High memory usage detected:', info);
                    }
                });
            }

            // 设置错误处理
            if (this.errorBoundary) {
                this.errorBoundary.onError((error) => {
                    this.handleError(error);
                });
            }

            // 检查依赖
            this.checkDependencies();

            // 初始化数据管理器
            await this.initializeDataManager();

            // 初始化组件
            await this.initializeComponents();

            // 绑定事件
            this.bindEvents();

            // 加载初始数据
            await this.loadInitialData();

            // 设置状态
            this.setState({ isInitialized: true });

            // 结束性能监控
            if (this.performanceMonitor) {
                this.performanceMonitor.mark('app-init-end');
                const measurement = this.performanceMonitor.measure('app-initialization', 'app-init-start', 'app-init-end');
                console.log(`App initialization took ${measurement.duration.toFixed(2)}ms`);
            }

            console.log('CryptoTradingApp initialized successfully');
            this.emit('initialized');

        } catch (error) {
            console.error('Failed to initialize CryptoTradingApp:', error);
            this.setState({ error: error.message });
            this.emit('error', error);
        }
    }

    /**
     * 检查依赖
     */
    checkDependencies() {
        const required = ['klinecharts', 'ChartComponent', 'TimeframeSelector', 'MarketDataPanel', 'MarketDataManager'];
        const missing = required.filter(dep => typeof window[dep] === 'undefined');
        
        if (missing.length > 0) {
            throw new Error(`Missing dependencies: ${missing.join(', ')}`);
        }
    }

    /**
     * 初始化数据管理器
     */
    async initializeDataManager() {
        this.marketDataManager = new MarketDataManager({
            apiBaseUrl: 'http://127.0.0.1:8001',
            wsBaseUrl: 'ws://127.0.0.1:8001'
        });
        
        // 订阅数据管理器事件
        this.marketDataManager.subscribe('dataUpdate', (data) => {
            this.handleDataUpdate(data);
        });
        
        this.marketDataManager.subscribe('marketDataUpdate', (data) => {
            this.handleMarketDataUpdate(data);
        });
        
        this.marketDataManager.subscribe('connected', (data) => {
            this.updateConnectionStatus(data.symbol, data.timeframe, true);
        });
        
        this.marketDataManager.subscribe('disconnected', (data) => {
            this.updateConnectionStatus(data.symbol, data.timeframe, false);
        });
        
        this.marketDataManager.subscribe('error', (error) => {
            this.handleError(error);
        });
    }

    /**
     * 初始化组件
     */
    async initializeComponents() {
        // 初始化图表组件
        const chartContainer = document.getElementById('main-chart');
        if (!chartContainer) {
            throw new Error('Chart container not found');
        }
        
        this.chartComponent = new ChartComponent(chartContainer, {
            theme: 'dark',
            showGrid: true,
            showCrosshair: true,
            showTooltip: true
        });
        
        // 初始化时间周期选择器
        this.timeframeSelector = new TimeframeSelector(chartContainer, {
            timeframes: this.config.timeframes,
            defaultTimeframe: this.config.defaultTimeframe,
            position: 'top-left',
            theme: 'dark'
        });
        
        // 初始化市场数据面板
        const marketDataContainer = document.getElementById('market-data-panel');
        if (marketDataContainer) {
            this.marketDataPanel = new MarketDataPanel(marketDataContainer, {
                symbols: this.config.symbols,
                theme: 'dark'
            });
        }
        
        console.log('Components initialized successfully');
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 时间周期选择器事件
        if (this.timeframeSelector) {
            this.timeframeSelector.on('timeframeChange', (timeframe) => {
                this.changeTimeframe(timeframe);
            });
        }
        
        // 市场数据面板事件
        if (this.marketDataPanel) {
            this.marketDataPanel.on('symbolClick', (symbol) => {
                this.changeSymbol(symbol);
            });
        }
        
        // 图表组件事件
        if (this.chartComponent) {
            this.chartComponent.on('error', (error) => {
                this.handleError(error);
            });
            
            this.chartComponent.on('crosshairChange', (params) => {
                this.handleCrosshairChange(params);
            });
        }
        
        // 窗口事件（使用防抖）
        window.addEventListener('resize', this.debouncedResize || (() => {
            this.handleResize();
        }));
        
        window.addEventListener('beforeunload', () => {
            this.destroy();
        });
        
        console.log('Events bound successfully');
    }

    /**
     * 加载初始数据
     */
    async loadInitialData() {
        try {
            this.setState({ isLoading: true });
            
            // 加载主图表数据
            await this.loadChartData(this.state.currentSymbol, this.state.currentTimeframe);
            
            // 连接实时数据流
            await this.connectToRealTimeData(this.state.currentSymbol, this.state.currentTimeframe);
            
            // 连接市场数据流
            for (const symbol of this.config.symbols) {
                await this.marketDataManager.connect(symbol, '1m'); // 使用1分钟数据获取实时价格
            }
            
            this.setState({ isLoading: false });
            
        } catch (error) {
            console.error('Failed to load initial data:', error);
            this.setState({ isLoading: false, error: error.message });
        }
    }

    /**
     * 加载图表数据
     * @param {string} symbol - 交易对符号
     * @param {string} timeframe - 时间周期
     */
    async loadChartData(symbol, timeframe) {
        try {
            console.log(`Loading chart data for ${symbol}_${timeframe}`);
            
            const data = await this.marketDataManager.getHistoricalData(symbol, timeframe, 500);
            
            if (this.chartComponent) {
                this.chartComponent.setSymbol(symbol);
                this.chartComponent.setTimeframe(timeframe);
                this.chartComponent.setData(data);
            }
            
            this.updateStatus(`${symbol} ${timeframe} 数据加载完成`);
            
        } catch (error) {
            console.error(`Failed to load chart data for ${symbol}_${timeframe}:`, error);
            this.updateStatus(`加载${symbol} ${timeframe}数据失败: ${error.message}`, 'error');
            throw error;
        }
    }

    /**
     * 连接实时数据流
     * @param {string} symbol - 交易对符号
     * @param {string} timeframe - 时间周期
     */
    async connectToRealTimeData(symbol, timeframe) {
        try {
            await this.marketDataManager.connect(symbol, timeframe);
            console.log(`Connected to real-time data for ${symbol}_${timeframe}`);
        } catch (error) {
            console.error(`Failed to connect to real-time data for ${symbol}_${timeframe}:`, error);
            throw error;
        }
    }

    /**
     * 切换交易对
     * @param {string} symbol - 新的交易对符号
     */
    async changeSymbol(symbol) {
        if (symbol === this.state.currentSymbol) return;
        
        try {
            console.log(`Changing symbol from ${this.state.currentSymbol} to ${symbol}`);
            
            this.setState({ isLoading: true });
            
            // 断开当前连接
            this.marketDataManager.disconnect(this.state.currentSymbol, this.state.currentTimeframe);
            
            // 更新状态
            this.setState({ currentSymbol: symbol });
            
            // 加载新数据
            await this.loadChartData(symbol, this.state.currentTimeframe);
            
            // 连接新的实时数据流
            await this.connectToRealTimeData(symbol, this.state.currentTimeframe);
            
            // 更新市场数据面板激活状态
            if (this.marketDataPanel) {
                this.marketDataPanel.setActiveSymbol(symbol);
            }
            
            this.setState({ isLoading: false });
            this.updateStatus(`已切换到 ${symbol}`);
            this.emit('symbolChanged', symbol);
            
        } catch (error) {
            console.error(`Failed to change symbol to ${symbol}:`, error);
            this.setState({ isLoading: false, error: error.message });
            this.updateStatus(`切换到${symbol}失败: ${error.message}`, 'error');
        }
    }

    /**
     * 切换时间周期
     * @param {string} timeframe - 新的时间周期
     */
    async changeTimeframe(timeframe) {
        if (timeframe === this.state.currentTimeframe) return;
        
        try {
            console.log(`Changing timeframe from ${this.state.currentTimeframe} to ${timeframe}`);
            
            this.setState({ isLoading: true });
            
            // 断开当前连接
            this.marketDataManager.disconnect(this.state.currentSymbol, this.state.currentTimeframe);
            
            // 更新状态
            this.setState({ currentTimeframe: timeframe });
            
            // 加载新数据
            await this.loadChartData(this.state.currentSymbol, timeframe);
            
            // 连接新的实时数据流
            await this.connectToRealTimeData(this.state.currentSymbol, timeframe);
            
            this.setState({ isLoading: false });
            this.updateStatus(`已切换到 ${timeframe} 周期`);
            this.emit('timeframeChanged', timeframe);
            
        } catch (error) {
            console.error(`Failed to change timeframe to ${timeframe}:`, error);
            this.setState({ isLoading: false, error: error.message });
            this.updateStatus(`切换到${timeframe}周期失败: ${error.message}`, 'error');
        }
    }

    /**
     * 处理数据更新
     * @param {Object} data - 数据更新事件
     */
    handleDataUpdate(data) {
        // 使用节流函数处理高频数据更新
        if (this.throttledDataUpdate) {
            this.throttledDataUpdate(data);
        } else {
            this._handleDataUpdateInternal(data);
        }
    }

    /**
     * 内部数据更新处理
     * @param {Object} data - 数据更新事件
     */
    _handleDataUpdateInternal(data) {
        if (data.symbol === this.state.currentSymbol && data.timeframe === this.state.currentTimeframe) {
            if (this.chartComponent) {
                this.chartComponent.updateData(data.data);
            }
        }
    }

    /**
     * 处理市场数据更新
     * @param {Object} data - 市场数据更新事件
     */
    handleMarketDataUpdate(data) {
        if (this.marketDataPanel) {
            this.marketDataPanel.updateSymbolData(data.symbol, data);
        }
    }

    /**
     * 处理十字光标变化
     * @param {Object} params - 十字光标参数
     */
    handleCrosshairChange(params) {
        // 这里可以添加十字光标变化时的逻辑
        // 比如更新技术指标显示等
    }

    /**
     * 处理窗口大小变化
     */
    handleResize() {
        if (this.chartComponent) {
            this.chartComponent.resize();
        }
    }

    /**
     * 处理错误
     * @param {Error} error - 错误对象
     */
    handleError(error) {
        console.error('Application error:', error);
        this.setState({ error: error.message });
        this.updateStatus(`错误: ${error.message}`, 'error');
        this.emit('error', error);
    }

    /**
     * 更新连接状态
     * @param {string} symbol - 交易对符号
     * @param {string} timeframe - 时间周期
     * @param {boolean} connected - 是否连接
     */
    updateConnectionStatus(symbol, timeframe, connected) {
        const status = connected ? '已连接' : '断开';
        this.updateStatus(`${symbol} ${timeframe} ${status}`);
        this.updateConnectionIndicator(connected);
    }

    /**
     * 更新连接指示器
     * @param {boolean} connected - 是否连接
     */
    updateConnectionIndicator(connected) {
        const dot = document.getElementById('connection-dot');
        const text = document.getElementById('connection-text');
        
        if (dot && text) {
            if (connected) {
                dot.classList.add('connected');
                text.textContent = '已连接';
            } else {
                dot.classList.remove('connected');
                text.textContent = '断开';
            }
        }
    }

    /**
     * 更新状态显示
     * @param {string} message - 状态消息
     * @param {string} type - 消息类型
     */
    updateStatus(message, type = 'info') {
        const statusElement = document.getElementById('status');
        if (statusElement) {
            statusElement.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            statusElement.className = `status ${type}`;
            
            // 3秒后清除错误状态
            if (type === 'error') {
                setTimeout(() => {
                    statusElement.className = 'status';
                }, 3000);
            }
        }
        console.log(`Status: ${message}`);
    }

    /**
     * 设置应用状态
     * @param {Object} newState - 新状态
     */
    setState(newState) {
        this.state = { ...this.state, ...newState };
        this.emit('stateChange', this.state);
    }

    /**
     * 获取应用状态
     * @returns {Object} 当前状态
     */
    getState() {
        return { ...this.state };
    }

    /**
     * 刷新数据
     */
    async refreshData() {
        try {
            this.updateStatus('正在刷新数据...');
            await this.loadChartData(this.state.currentSymbol, this.state.currentTimeframe);
            this.updateStatus('数据刷新完成');
        } catch (error) {
            this.handleError(error);
        }
    }

    /**
     * 清除图表
     */
    clearChart() {
        if (this.chartComponent) {
            this.chartComponent.clearData();
            this.updateStatus('图表已清除');
        }
    }

    /**
     * 事件监听
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    /**
     * 移除事件监听
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    off(event, callback) {
        if (this.eventListeners.has(event)) {
            const listeners = this.eventListeners.get(event);
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }

    /**
     * 触发事件
     * @param {string} event - 事件名称
     * @param {*} data - 事件数据
     */
    emit(event, data) {
        if (this.eventListeners.has(event)) {
            this.eventListeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in event listener for ${event}:`, error);
                }
            });
        }
    }

    /**
     * 销毁应用
     */
    destroy() {
        try {
            // 停止性能监控
            if (this.memoryMonitor) {
                this.memoryMonitor.stop();
            }

            // 清理所有资源
            if (this.resourceCleaner) {
                this.resourceCleaner.cleanupAll();
            }

            // 销毁组件
            if (this.chartComponent) {
                this.chartComponent.destroy();
            }

            if (this.timeframeSelector) {
                this.timeframeSelector.destroy();
            }

            if (this.marketDataPanel) {
                this.marketDataPanel.destroy();
            }

            // 销毁管理器
            if (this.marketDataManager) {
                this.marketDataManager.destroy();
            }

            // 清理事件监听器
            this.eventListeners.clear();

            // 清理性能指标
            if (this.performanceMonitor) {
                this.performanceMonitor.clearMetrics();
            }

            console.log('CryptoTradingApp destroyed');

        } catch (error) {
            console.error('Failed to destroy CryptoTradingApp:', error);
        }
    }
}

// 全局函数供HTML调用
window.refreshData = () => {
    if (window.app) {
        window.app.refreshData();
    }
};

window.clearCharts = () => {
    if (window.app) {
        window.app.clearChart();
    }
};

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CryptoTradingApp;
} else {
    window.CryptoTradingApp = CryptoTradingApp;
}
