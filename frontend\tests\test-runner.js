/**
 * 简单的测试运行器
 */
class TestRunner {
    constructor() {
        this.tests = [];
        this.results = {
            passed: 0,
            failed: 0,
            total: 0,
            errors: []
        };
    }

    /**
     * 添加测试
     * @param {string} name - 测试名称
     * @param {Function} testFn - 测试函数
     */
    test(name, testFn) {
        this.tests.push({ name, testFn });
    }

    /**
     * 运行所有测试
     */
    async run() {
        console.log('🧪 Running tests...');
        console.log('='.repeat(50));
        
        this.results = {
            passed: 0,
            failed: 0,
            total: this.tests.length,
            errors: []
        };

        for (const test of this.tests) {
            await this.runSingleTest(test);
        }

        this.printResults();
        return this.results;
    }

    /**
     * 运行单个测试
     * @param {Object} test - 测试对象
     */
    async runSingleTest(test) {
        try {
            console.log(`Running: ${test.name}`);
            await test.testFn();
            console.log(`✅ ${test.name}`);
            this.results.passed++;
        } catch (error) {
            console.error(`❌ ${test.name}: ${error.message}`);
            this.results.failed++;
            this.results.errors.push({
                test: test.name,
                error: error.message,
                stack: error.stack
            });
        }
    }

    /**
     * 打印测试结果
     */
    printResults() {
        console.log('='.repeat(50));
        console.log('📊 Test Results:');
        console.log(`Total: ${this.results.total}`);
        console.log(`Passed: ${this.results.passed}`);
        console.log(`Failed: ${this.results.failed}`);
        
        if (this.results.failed > 0) {
            console.log('\n❌ Failed tests:');
            this.results.errors.forEach(error => {
                console.log(`  - ${error.test}: ${error.error}`);
            });
        }
        
        const successRate = ((this.results.passed / this.results.total) * 100).toFixed(1);
        console.log(`\nSuccess rate: ${successRate}%`);
    }
}

/**
 * 断言函数
 */
const assert = {
    /**
     * 断言值为真
     * @param {*} value - 要检查的值
     * @param {string} message - 错误消息
     */
    isTrue(value, message = 'Expected value to be true') {
        if (!value) {
            throw new Error(message);
        }
    },

    /**
     * 断言值为假
     * @param {*} value - 要检查的值
     * @param {string} message - 错误消息
     */
    isFalse(value, message = 'Expected value to be false') {
        if (value) {
            throw new Error(message);
        }
    },

    /**
     * 断言相等
     * @param {*} actual - 实际值
     * @param {*} expected - 期望值
     * @param {string} message - 错误消息
     */
    equals(actual, expected, message = `Expected ${actual} to equal ${expected}`) {
        if (actual !== expected) {
            throw new Error(message);
        }
    },

    /**
     * 断言不相等
     * @param {*} actual - 实际值
     * @param {*} expected - 期望值
     * @param {string} message - 错误消息
     */
    notEquals(actual, expected, message = `Expected ${actual} not to equal ${expected}`) {
        if (actual === expected) {
            throw new Error(message);
        }
    },

    /**
     * 断言深度相等
     * @param {*} actual - 实际值
     * @param {*} expected - 期望值
     * @param {string} message - 错误消息
     */
    deepEquals(actual, expected, message = 'Objects are not deeply equal') {
        if (!this._deepEqual(actual, expected)) {
            throw new Error(message);
        }
    },

    /**
     * 断言抛出错误
     * @param {Function} fn - 要执行的函数
     * @param {string} message - 错误消息
     */
    throws(fn, message = 'Expected function to throw an error') {
        try {
            fn();
            throw new Error(message);
        } catch (error) {
            if (error.message === message) {
                throw error;
            }
            // 函数确实抛出了错误，测试通过
        }
    },

    /**
     * 断言不抛出错误
     * @param {Function} fn - 要执行的函数
     * @param {string} message - 错误消息
     */
    doesNotThrow(fn, message = 'Expected function not to throw an error') {
        try {
            fn();
        } catch (error) {
            throw new Error(`${message}: ${error.message}`);
        }
    },

    /**
     * 断言值存在
     * @param {*} value - 要检查的值
     * @param {string} message - 错误消息
     */
    exists(value, message = 'Expected value to exist') {
        if (value === null || value === undefined) {
            throw new Error(message);
        }
    },

    /**
     * 断言值不存在
     * @param {*} value - 要检查的值
     * @param {string} message - 错误消息
     */
    notExists(value, message = 'Expected value not to exist') {
        if (value !== null && value !== undefined) {
            throw new Error(message);
        }
    },

    /**
     * 断言是数组
     * @param {*} value - 要检查的值
     * @param {string} message - 错误消息
     */
    isArray(value, message = 'Expected value to be an array') {
        if (!Array.isArray(value)) {
            throw new Error(message);
        }
    },

    /**
     * 断言是对象
     * @param {*} value - 要检查的值
     * @param {string} message - 错误消息
     */
    isObject(value, message = 'Expected value to be an object') {
        if (typeof value !== 'object' || value === null || Array.isArray(value)) {
            throw new Error(message);
        }
    },

    /**
     * 断言是函数
     * @param {*} value - 要检查的值
     * @param {string} message - 错误消息
     */
    isFunction(value, message = 'Expected value to be a function') {
        if (typeof value !== 'function') {
            throw new Error(message);
        }
    },

    /**
     * 深度比较两个值
     * @param {*} a - 值A
     * @param {*} b - 值B
     * @returns {boolean} 是否相等
     */
    _deepEqual(a, b) {
        if (a === b) return true;
        
        if (a == null || b == null) return false;
        
        if (Array.isArray(a) && Array.isArray(b)) {
            if (a.length !== b.length) return false;
            for (let i = 0; i < a.length; i++) {
                if (!this._deepEqual(a[i], b[i])) return false;
            }
            return true;
        }
        
        if (typeof a === 'object' && typeof b === 'object') {
            const keysA = Object.keys(a);
            const keysB = Object.keys(b);
            
            if (keysA.length !== keysB.length) return false;
            
            for (const key of keysA) {
                if (!keysB.includes(key)) return false;
                if (!this._deepEqual(a[key], b[key])) return false;
            }
            return true;
        }
        
        return false;
    }
};

/**
 * Mock工厂
 */
class MockFactory {
    /**
     * 创建Mock函数
     * @param {*} returnValue - 返回值
     * @returns {Function} Mock函数
     */
    static createMockFunction(returnValue) {
        const mockFn = function(...args) {
            mockFn.calls.push(args);
            mockFn.callCount++;
            return returnValue;
        };
        
        mockFn.calls = [];
        mockFn.callCount = 0;
        mockFn.reset = () => {
            mockFn.calls = [];
            mockFn.callCount = 0;
        };
        
        return mockFn;
    }

    /**
     * 创建Mock对象
     * @param {Object} methods - 方法定义
     * @returns {Object} Mock对象
     */
    static createMockObject(methods = {}) {
        const mock = {};
        
        for (const [name, returnValue] of Object.entries(methods)) {
            mock[name] = this.createMockFunction(returnValue);
        }
        
        return mock;
    }

    /**
     * 创建WebSocket Mock
     * @returns {Object} WebSocket Mock
     */
    static createWebSocketMock() {
        const mock = {
            readyState: 1, // OPEN
            send: this.createMockFunction(),
            close: this.createMockFunction(),
            addEventListener: this.createMockFunction(),
            removeEventListener: this.createMockFunction(),
            
            // 模拟事件触发
            triggerOpen() {
                if (this.onopen) this.onopen();
            },
            
            triggerMessage(data) {
                if (this.onmessage) this.onmessage({ data });
            },
            
            triggerError(error) {
                if (this.onerror) this.onerror(error);
            },
            
            triggerClose(event = {}) {
                if (this.onclose) this.onclose(event);
            }
        };
        
        return mock;
    }
}

// 创建全局测试运行器实例
const testRunner = new TestRunner();

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        TestRunner,
        assert,
        MockFactory,
        testRunner
    };
} else {
    window.TestUtils = {
        TestRunner,
        assert,
        MockFactory,
        testRunner
    };
}
