/**
 * 图表组件 - 负责K线图的渲染和管理
 * 遵循单一职责原则，只处理图表相关逻辑
 */
class ChartComponent {
    constructor(container, config = {}) {
        this.container = container;
        this.config = {
            theme: 'dark',
            showGrid: true,
            showCrosshair: true,
            showTooltip: true,
            ...config
        };
        
        this.chart = null;
        this.currentTimeframe = '1h';
        this.currentSymbol = 'BTCUSDT';
        this.data = [];
        this.eventListeners = new Map();
        
        this.initialize();
    }

    /**
     * 初始化图表
     */
    initialize() {
        try {
            if (!this.container) {
                throw new Error('Chart container is required');
            }

            // 检查klinecharts是否可用
            if (typeof klinecharts === 'undefined') {
                throw new Error('KLineChart library not loaded');
            }

            // 创建图表实例
            this.chart = klinecharts.init(this.container);
            
            // 应用样式配置
            this.applyStyles();
            
            // 设置事件监听
            this.setupEventListeners();
            
            console.log('ChartComponent initialized successfully');
            this.emit('initialized');
            
        } catch (error) {
            console.error('Failed to initialize ChartComponent:', error);
            this.emit('error', error);
        }
    }

    /**
     * 应用图表样式
     */
    applyStyles() {
        const chartStyle = {
            grid: {
                show: this.config.showGrid,
                horizontal: { 
                    show: true, 
                    color: '#393939', 
                    style: 'dashed' 
                },
                vertical: { 
                    show: true, 
                    color: '#393939', 
                    style: 'dashed' 
                }
            },
            candle: {
                type: 'candle_solid',
                bar: {
                    upColor: '#26A69A',
                    downColor: '#EF5350',
                    noChangeColor: '#888888'
                },
                tooltip: { 
                    showRule: this.config.showTooltip ? 'always' : 'none', 
                    showType: 'standard' 
                }
            },
            technicalIndicator: {
                margin: { top: 0.2, bottom: 0.1 },
                bar: { 
                    upColor: '#26A69A', 
                    downColor: '#EF5350', 
                    noChangeColor: '#888888' 
                },
                line: { 
                    size: 1, 
                    colors: ['#FF9600', '#9D65C9', '#2196F3', '#E11D74', '#01C5C4'] 
                },
                circle: { 
                    upColor: '#26A69A', 
                    downColor: '#EF5350', 
                    noChangeColor: '#888888' 
                }
            },
            crosshair: {
                show: this.config.showCrosshair,
                horizontal: {
                    show: true,
                    line: { color: '#888888', style: 'dashed' }
                },
                vertical: {
                    show: true,
                    line: { color: '#888888', style: 'dashed' }
                }
            }
        };

        this.chart.setStyles(chartStyle);
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        if (!this.chart) return;

        // 十字光标变化事件
        this.chart.subscribeAction('onCrosshairChange', (params) => {
            this.emit('crosshairChange', params);
        });

        // 点击事件
        this.chart.subscribeAction('onClick', (params) => {
            this.emit('click', params);
        });

        // 缩放事件
        this.chart.subscribeAction('onZoom', (params) => {
            this.emit('zoom', params);
        });

        // 数据就绪事件
        this.chart.subscribeAction('onDataReady', () => {
            this.emit('dataReady');
        });
    }

    /**
     * 设置图表数据
     * @param {Array} data - K线数据数组
     */
    setData(data) {
        try {
            if (!this.chart) {
                throw new Error('Chart not initialized');
            }

            if (!Array.isArray(data)) {
                throw new Error('Data must be an array');
            }

            this.data = data;
            this.chart.applyNewData(data);
            
            // 添加技术指标
            this.addTechnicalIndicators();
            
            this.emit('dataSet', data);
            console.log(`Chart data set: ${data.length} records`);
            
        } catch (error) {
            console.error('Failed to set chart data:', error);
            this.emit('error', error);
        }
    }

    /**
     * 更新图表数据
     * @param {Object} newData - 新的K线数据
     */
    updateData(newData) {
        try {
            if (!this.chart) {
                throw new Error('Chart not initialized');
            }

            if (!newData || typeof newData !== 'object') {
                throw new Error('Invalid data format');
            }

            this.chart.updateData(newData);
            
            // 更新本地数据缓存
            if (this.data.length > 0) {
                const lastIndex = this.data.length - 1;
                if (this.data[lastIndex].timestamp === newData.timestamp) {
                    this.data[lastIndex] = newData;
                } else {
                    this.data.push(newData);
                }
            }
            
            this.emit('dataUpdated', newData);
            
        } catch (error) {
            console.error('Failed to update chart data:', error);
            this.emit('error', error);
        }
    }

    /**
     * 清除图表数据
     */
    clearData() {
        try {
            if (this.chart) {
                this.chart.applyNewData([]);
                this.data = [];
                this.emit('dataCleared');
            }
        } catch (error) {
            console.error('Failed to clear chart data:', error);
            this.emit('error', error);
        }
    }

    /**
     * 设置时间周期
     * @param {string} timeframe - 时间周期 (1m, 5m, 15m, 1h, 4h, 1d等)
     */
    setTimeframe(timeframe) {
        if (this.currentTimeframe !== timeframe) {
            this.currentTimeframe = timeframe;
            this.emit('timeframeChanged', timeframe);
        }
    }

    /**
     * 获取当前时间周期
     * @returns {string} 当前时间周期
     */
    getTimeframe() {
        return this.currentTimeframe;
    }

    /**
     * 设置交易对
     * @param {string} symbol - 交易对符号
     */
    setSymbol(symbol) {
        if (this.currentSymbol !== symbol) {
            this.currentSymbol = symbol;
            this.emit('symbolChanged', symbol);
        }
    }

    /**
     * 获取当前交易对
     * @returns {string} 当前交易对
     */
    getSymbol() {
        return this.currentSymbol;
    }

    /**
     * 添加技术指标
     */
    addTechnicalIndicators() {
        try {
            if (!this.chart) return;

            // 添加移动平均线
            this.chart.createTechnicalIndicator('MA', false, { 
                id: 'MA', 
                calcParams: [5, 10, 20, 50] 
            });

            // 添加MACD指标
            this.chart.createTechnicalIndicator('MACD', true, { 
                id: 'MACD' 
            });

            // 添加RSI指标
            this.chart.createTechnicalIndicator('RSI', true, { 
                id: 'RSI' 
            });

        } catch (error) {
            console.error('Failed to add technical indicators:', error);
        }
    }

    /**
     * 移除技术指标
     * @param {string} indicatorId - 指标ID
     */
    removeTechnicalIndicator(indicatorId) {
        try {
            if (this.chart) {
                this.chart.removeTechnicalIndicator(indicatorId);
            }
        } catch (error) {
            console.error('Failed to remove technical indicator:', error);
        }
    }

    /**
     * 调整图表大小
     */
    resize() {
        try {
            if (this.chart) {
                this.chart.resize();
                this.emit('resized');
            }
        } catch (error) {
            console.error('Failed to resize chart:', error);
        }
    }

    /**
     * 销毁图表
     */
    destroy() {
        try {
            if (this.chart) {
                this.chart.dispose();
                this.chart = null;
            }
            
            this.data = [];
            this.eventListeners.clear();
            this.emit('destroyed');
            
        } catch (error) {
            console.error('Failed to destroy chart:', error);
        }
    }

    /**
     * 事件监听
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    /**
     * 移除事件监听
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    off(event, callback) {
        if (this.eventListeners.has(event)) {
            const listeners = this.eventListeners.get(event);
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }

    /**
     * 触发事件
     * @param {string} event - 事件名称
     * @param {*} data - 事件数据
     */
    emit(event, data) {
        if (this.eventListeners.has(event)) {
            this.eventListeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in event listener for ${event}:`, error);
                }
            });
        }
    }
}

// 导出组件
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ChartComponent;
} else {
    window.ChartComponent = ChartComponent;
}
