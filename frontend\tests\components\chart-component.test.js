/**
 * ChartComponent 单元测试
 */

// 模拟klinecharts库
window.klinecharts = {
    init: TestUtils.MockFactory.createMockFunction({
        setStyles: TestUtils.MockFactory.createMockFunction(),
        applyNewData: TestUtils.MockFactory.createMockFunction(),
        updateData: TestUtils.MockFactory.createMockFunction(),
        createTechnicalIndicator: TestUtils.MockFactory.createMockFunction(),
        removeTechnicalIndicator: TestUtils.MockFactory.createMockFunction(),
        subscribeAction: TestUtils.MockFactory.createMockFunction(),
        resize: TestUtils.MockFactory.createMockFunction(),
        dispose: TestUtils.MockFactory.createMockFunction()
    })
};

// ChartComponent 测试
TestUtils.testRunner.test('ChartComponent - 初始化', () => {
    const container = document.createElement('div');
    const chart = new ChartComponent(container);
    
    TestUtils.assert.exists(chart, 'ChartComponent should be created');
    TestUtils.assert.equals(chart.currentTimeframe, '1h', 'Default timeframe should be 1h');
    TestUtils.assert.equals(chart.currentSymbol, 'BTCUSDT', 'Default symbol should be BTCUSDT');
});

TestUtils.testRunner.test('ChartComponent - 设置数据', () => {
    const container = document.createElement('div');
    const chart = new ChartComponent(container);
    
    const testData = [
        {
            timestamp: Date.now(),
            open: 100,
            high: 110,
            low: 90,
            close: 105,
            volume: 1000
        }
    ];
    
    TestUtils.assert.doesNotThrow(() => {
        chart.setData(testData);
    }, 'setData should not throw error');
    
    TestUtils.assert.equals(chart.data.length, 1, 'Data should be stored');
});

TestUtils.testRunner.test('ChartComponent - 更新数据', () => {
    const container = document.createElement('div');
    const chart = new ChartComponent(container);
    
    // 先设置初始数据
    const initialData = [
        {
            timestamp: Date.now(),
            open: 100,
            high: 110,
            low: 90,
            close: 105,
            volume: 1000
        }
    ];
    chart.setData(initialData);
    
    // 更新数据
    const updateData = {
        timestamp: Date.now() + 1000,
        open: 105,
        high: 115,
        low: 95,
        close: 110,
        volume: 1200
    };
    
    TestUtils.assert.doesNotThrow(() => {
        chart.updateData(updateData);
    }, 'updateData should not throw error');
});

TestUtils.testRunner.test('ChartComponent - 设置时间周期', () => {
    const container = document.createElement('div');
    const chart = new ChartComponent(container);
    
    let eventTriggered = false;
    chart.on('timeframeChanged', (timeframe) => {
        eventTriggered = true;
        TestUtils.assert.equals(timeframe, '5m', 'Event should pass correct timeframe');
    });
    
    chart.setTimeframe('5m');
    
    TestUtils.assert.equals(chart.getTimeframe(), '5m', 'Timeframe should be updated');
    TestUtils.assert.isTrue(eventTriggered, 'timeframeChanged event should be triggered');
});

TestUtils.testRunner.test('ChartComponent - 设置交易对', () => {
    const container = document.createElement('div');
    const chart = new ChartComponent(container);
    
    let eventTriggered = false;
    chart.on('symbolChanged', (symbol) => {
        eventTriggered = true;
        TestUtils.assert.equals(symbol, 'ETHUSDT', 'Event should pass correct symbol');
    });
    
    chart.setSymbol('ETHUSDT');
    
    TestUtils.assert.equals(chart.getSymbol(), 'ETHUSDT', 'Symbol should be updated');
    TestUtils.assert.isTrue(eventTriggered, 'symbolChanged event should be triggered');
});

TestUtils.testRunner.test('ChartComponent - 事件系统', () => {
    const container = document.createElement('div');
    const chart = new ChartComponent(container);
    
    let callCount = 0;
    const testCallback = () => {
        callCount++;
    };
    
    // 添加事件监听器
    chart.on('test', testCallback);
    
    // 触发事件
    chart.emit('test');
    TestUtils.assert.equals(callCount, 1, 'Event callback should be called once');
    
    // 再次触发
    chart.emit('test');
    TestUtils.assert.equals(callCount, 2, 'Event callback should be called twice');
    
    // 移除事件监听器
    chart.off('test', testCallback);
    chart.emit('test');
    TestUtils.assert.equals(callCount, 2, 'Event callback should not be called after removal');
});

TestUtils.testRunner.test('ChartComponent - 清除数据', () => {
    const container = document.createElement('div');
    const chart = new ChartComponent(container);
    
    // 设置数据
    const testData = [{ timestamp: Date.now(), open: 100, high: 110, low: 90, close: 105, volume: 1000 }];
    chart.setData(testData);
    
    TestUtils.assert.equals(chart.data.length, 1, 'Data should be set');
    
    // 清除数据
    chart.clearData();
    TestUtils.assert.equals(chart.data.length, 0, 'Data should be cleared');
});

TestUtils.testRunner.test('ChartComponent - 错误处理', () => {
    // 测试无效容器
    TestUtils.assert.throws(() => {
        new ChartComponent(null);
    }, 'Should throw error for null container');
    
    const container = document.createElement('div');
    const chart = new ChartComponent(container);
    
    // 测试无效数据
    TestUtils.assert.throws(() => {
        chart.setData('invalid data');
    }, 'Should throw error for invalid data');
    
    TestUtils.assert.throws(() => {
        chart.updateData(null);
    }, 'Should throw error for null update data');
});

TestUtils.testRunner.test('ChartComponent - 销毁', () => {
    const container = document.createElement('div');
    const chart = new ChartComponent(container);
    
    let destroyEventTriggered = false;
    chart.on('destroyed', () => {
        destroyEventTriggered = true;
    });
    
    TestUtils.assert.doesNotThrow(() => {
        chart.destroy();
    }, 'destroy should not throw error');
    
    TestUtils.assert.isTrue(destroyEventTriggered, 'destroyed event should be triggered');
    TestUtils.assert.equals(chart.chart, null, 'Chart instance should be null after destroy');
    TestUtils.assert.equals(chart.data.length, 0, 'Data should be cleared after destroy');
});

console.log('ChartComponent tests loaded');
