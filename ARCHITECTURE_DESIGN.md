# 加密货币交易UI架构设计文档

## 项目概述
重构现有的4图表显示为单图表显示，添加K周期切换按钮和实时行情显示块，遵循模块化、松耦合、高内聚的设计原则。

## 核心设计原则

### 1. 模块化 (Modularity)
- 每个功能模块独立封装
- 清晰的模块边界和职责分离
- 可独立开发、测试和维护

### 2. 松耦合 (Loose Coupling)
- 模块间通过标准接口通信
- 事件驱动的架构模式
- 依赖注入和配置管理

### 3. 高内聚 (High Cohesion)
- 相关功能集中在同一模块
- 单一职责原则
- 清晰的数据流和状态管理

### 4. 可测试性 (Testability)
- 纯函数和可预测的状态变化
- Mock友好的接口设计
- 单元测试覆盖率目标 > 80%

### 5. 性能优先 (Performance First)
- 数据缓存和防抖处理
- 虚拟化和懒加载
- 内存泄漏防护

## 新架构组件设计

### 1. 核心组件层 (Core Components)

#### ChartComponent
```javascript
class ChartComponent {
  constructor(container, config) {}
  
  // 图表管理
  initialize()
  destroy()
  resize()
  
  // 数据管理
  setData(data)
  updateData(newData)
  clearData()
  
  // 时间周期管理
  setTimeframe(timeframe)
  getTimeframe()
  
  // 事件处理
  on(event, callback)
  off(event, callback)
  emit(event, data)
}
```

#### TimeframeSelector
```javascript
class TimeframeSelector {
  constructor(container, options) {}
  
  // 渲染和交互
  render()
  setActive(timeframe)
  getActive()
  
  // 事件处理
  onTimeframeChange(callback)
}
```

#### MarketDataPanel
```javascript
class MarketDataPanel {
  constructor(container, symbols) {}
  
  // 数据显示
  updateSymbolData(symbol, data)
  setActiveSymbol(symbol)
  
  // 颜色变化动画
  animateChange(symbol, oldValue, newValue)
  
  // 事件处理
  onSymbolClick(callback)
}
```

### 2. 数据管理层 (Data Management)

#### MarketDataManager
```javascript
class MarketDataManager {
  constructor(config) {}
  
  // 连接管理
  connect(symbol, timeframe)
  disconnect(symbol, timeframe)
  reconnect()
  
  // 数据缓存
  getCachedData(symbol, timeframe)
  setCachedData(symbol, timeframe, data)
  
  // 状态管理
  getConnectionStatus()
  getDataStatus()
  
  // 事件发布
  subscribe(event, callback)
  unsubscribe(event, callback)
}
```

#### WebSocketManager
```javascript
class WebSocketManager {
  constructor() {}
  
  // 连接池管理
  createConnection(url, options)
  closeConnection(id)
  getConnection(id)
  
  // 重连机制
  enableAutoReconnect(id, options)
  disableAutoReconnect(id)
  
  // 消息处理
  send(id, message)
  onMessage(id, callback)
  onError(id, callback)
}
```

### 3. 应用层 (Application Layer)

#### AppController
```javascript
class AppController {
  constructor() {}
  
  // 应用初始化
  initialize()
  destroy()
  
  // 组件协调
  setupComponents()
  bindEvents()
  
  // 状态管理
  setState(state)
  getState()
  
  // 错误处理
  handleError(error)
}
```

## 文件结构设计

```
frontend/
├── src/
│   ├── components/           # 组件层
│   │   ├── chart/
│   │   │   ├── ChartComponent.js
│   │   │   ├── TimeframeSelector.js
│   │   │   └── chart.css
│   │   ├── market/
│   │   │   ├── MarketDataPanel.js
│   │   │   ├── SymbolCard.js
│   │   │   └── market.css
│   │   └── indicators/
│   │       ├── TechnicalIndicators.js
│   │       └── indicators.css
│   ├── managers/             # 数据管理层
│   │   ├── MarketDataManager.js
│   │   ├── WebSocketManager.js
│   │   └── CacheManager.js
│   ├── utils/                # 工具函数
│   │   ├── formatters.js
│   │   ├── validators.js
│   │   ├── debounce.js
│   │   └── constants.js
│   ├── config/               # 配置文件
│   │   ├── app.config.js
│   │   ├── chart.config.js
│   │   └── api.config.js
│   ├── styles/               # 样式文件
│   │   ├── main.css
│   │   ├── variables.css
│   │   └── animations.css
│   └── app.js                # 应用入口
├── tests/                    # 测试文件
│   ├── unit/
│   ├── integration/
│   └── e2e/
└── dist/                     # 构建输出
```

## 数据流设计

### 1. 实时数据流
```
WebSocket → MarketDataManager → Components → UI Update
```

### 2. 用户交互流
```
User Action → Component Event → AppController → State Update → UI Sync
```

### 3. 错误处理流
```
Error → ErrorBoundary → Logger → User Notification → Recovery
```

## 性能优化策略

### 1. 数据缓存
- LRU缓存策略
- 分层缓存（内存 + LocalStorage）
- 缓存失效机制

### 2. 渲染优化
- 虚拟滚动
- 防抖和节流
- RAF优化动画

### 3. 内存管理
- 组件生命周期管理
- 事件监听器清理
- WebSocket连接池

## 接口标准化

### 1. 组件接口
```javascript
interface Component {
  initialize(): void
  destroy(): void
  render(): void
  update(data: any): void
}
```

### 2. 数据接口
```javascript
interface MarketData {
  symbol: string
  price: number
  change: number
  changePercent: number
  timestamp: number
}
```

### 3. 事件接口
```javascript
interface EventEmitter {
  on(event: string, callback: Function): void
  off(event: string, callback: Function): void
  emit(event: string, data: any): void
}
```

## 下一步实施计划

1. 创建基础组件架构
2. 实现单图表显示功能
3. 添加K周期切换按钮
4. 创建实时行情显示组件
5. 实现数据管理模块
6. 添加交互功能
7. 性能优化和测试
