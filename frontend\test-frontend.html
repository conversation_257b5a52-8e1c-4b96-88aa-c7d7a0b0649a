<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端功能测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #1a1a1a;
            color: #ffffff;
            margin: 0;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .test-section {
            background: #2c3e50;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .test-section h2 {
            color: #3498db;
            margin-top: 0;
        }

        .test-item {
            background: #34495e;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 10px;
        }

        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
        }

        .success {
            background: #27ae60;
            color: white;
        }

        .error {
            background: #e74c3c;
            color: white;
        }

        .warning {
            background: #f39c12;
            color: white;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }

        .btn:hover {
            background: #2980b9;
        }

        #market-data-panel {
            background: #2c3e50;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }

        #main-chart {
            background: #1a1a1a;
            height: 400px;
            border-radius: 8px;
            position: relative;
            margin: 16px 0;
        }

        .log-output {
            background: #1a1a1a;
            border: 1px solid #34495e;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 前端功能测试</h1>
        
        <div class="test-section">
            <h2>1. 依赖检查</h2>
            <div class="test-item">
                <button class="btn" onclick="checkDependencies()">检查依赖</button>
                <div id="dependencies-result" class="test-result"></div>
            </div>
        </div>

        <div class="test-section">
            <h2>2. 组件测试</h2>
            
            <div class="test-item">
                <h3>市场数据面板</h3>
                <div id="market-data-panel"></div>
                <button class="btn" onclick="testMarketDataPanel()">测试市场数据面板</button>
                <div id="market-panel-result" class="test-result"></div>
            </div>

            <div class="test-item">
                <h3>图表组件</h3>
                <div id="main-chart"></div>
                <button class="btn" onclick="testChartComponent()">测试图表组件</button>
                <div id="chart-result" class="test-result"></div>
            </div>
        </div>

        <div class="test-section">
            <h2>3. 日志系统测试</h2>
            <div class="test-item">
                <button class="btn" onclick="testLogger()">测试日志系统</button>
                <button class="btn" onclick="downloadTestLogs()">下载日志</button>
                <button class="btn" onclick="clearTestLogs()">清除日志</button>
                <div id="logger-result" class="test-result"></div>
                <div class="log-output" id="log-output"></div>
            </div>
        </div>

        <div class="test-section">
            <h2>4. 数据管理器测试</h2>
            <div class="test-item">
                <button class="btn" onclick="testDataManager()">测试数据管理器</button>
                <div id="data-manager-result" class="test-result"></div>
            </div>
        </div>
    </div>

    <!-- 加载脚本 -->
    <script src="./src/utils/performance.js"></script>
    <script src="./src/utils/logger.js"></script>
    <script src="./src/components/chart/ChartComponent.js"></script>
    <script src="./src/components/chart/TimeframeSelector.js"></script>
    <script src="./src/components/market/MarketDataPanel.js"></script>
    <script src="./src/managers/MarketDataManager.js"></script>

    <script>
        // 模拟klinecharts库
        window.klinecharts = {
            init: function(container) {
                console.log('Mock klinecharts initialized');
                return {
                    setStyles: () => {},
                    applyNewData: () => {},
                    updateData: () => {},
                    createTechnicalIndicator: () => {},
                    removeTechnicalIndicator: () => {},
                    subscribeAction: () => {},
                    resize: () => {},
                    dispose: () => {}
                };
            }
        };

        // 设置日志输出到页面
        if (window.logger) {
            window.logger.onUILog((logEntry) => {
                const logOutput = document.getElementById('log-output');
                if (logOutput) {
                    const timestamp = new Date(logEntry.timestamp).toLocaleTimeString();
                    const line = `[${timestamp}] [${logEntry.level}] ${logEntry.message}\n`;
                    logOutput.textContent += line;
                    logOutput.scrollTop = logOutput.scrollHeight;
                }
            });
        }

        function checkDependencies() {
            const result = document.getElementById('dependencies-result');
            const required = ['klinecharts', 'ChartComponent', 'TimeframeSelector', 'MarketDataPanel', 'MarketDataManager', 'Logger'];
            const missing = required.filter(dep => typeof window[dep] === 'undefined');
            
            if (missing.length === 0) {
                result.className = 'test-result success';
                result.textContent = '✅ 所有依赖都已加载';
                console.log('All dependencies loaded successfully');
            } else {
                result.className = 'test-result error';
                result.textContent = `❌ 缺少依赖: ${missing.join(', ')}`;
                console.error('Missing dependencies:', missing);
            }
        }

        function testMarketDataPanel() {
            const result = document.getElementById('market-panel-result');
            try {
                const container = document.getElementById('market-data-panel');
                const panel = new MarketDataPanel(container, {
                    symbols: ['BTCUSDT', 'ETHUSDT', 'SOLUSDT'],
                    theme: 'dark'
                });

                // 测试数据更新
                panel.updateSymbolData('BTCUSDT', {
                    price: 50000,
                    change: 1000,
                    changePercent: 2.5,
                    volume: 1000000,
                    high24h: 51000,
                    low24h: 49000
                });

                panel.updateSymbolData('ETHUSDT', {
                    price: 3000,
                    change: -50,
                    changePercent: -1.6,
                    volume: 500000,
                    high24h: 3100,
                    low24h: 2950
                });

                panel.updateSymbolData('SOLUSDT', {
                    price: 100,
                    change: 5,
                    changePercent: 5.2,
                    volume: 200000,
                    high24h: 105,
                    low24h: 95
                });

                result.className = 'test-result success';
                result.textContent = '✅ 市场数据面板测试成功';
                console.log('MarketDataPanel test passed');
            } catch (error) {
                result.className = 'test-result error';
                result.textContent = `❌ 市场数据面板测试失败: ${error.message}`;
                console.error('MarketDataPanel test failed:', error);
            }
        }

        function testChartComponent() {
            const result = document.getElementById('chart-result');
            try {
                const container = document.getElementById('main-chart');
                const chart = new ChartComponent(container, {
                    theme: 'dark'
                });

                // 测试数据设置
                const testData = [
                    {
                        timestamp: Date.now(),
                        open: 100,
                        high: 110,
                        low: 90,
                        close: 105,
                        volume: 1000
                    }
                ];

                chart.setData(testData);
                chart.setSymbol('BTCUSDT');
                chart.setTimeframe('1h');

                result.className = 'test-result success';
                result.textContent = '✅ 图表组件测试成功';
                console.log('ChartComponent test passed');
            } catch (error) {
                result.className = 'test-result error';
                result.textContent = `❌ 图表组件测试失败: ${error.message}`;
                console.error('ChartComponent test failed:', error);
            }
        }

        function testLogger() {
            const result = document.getElementById('logger-result');
            try {
                console.log('测试信息日志');
                console.warn('测试警告日志');
                console.error('测试错误日志');

                const logs = window.logger.getLogs();
                
                result.className = 'test-result success';
                result.textContent = `✅ 日志系统测试成功，共 ${logs.length} 条日志`;
            } catch (error) {
                result.className = 'test-result error';
                result.textContent = `❌ 日志系统测试失败: ${error.message}`;
            }
        }

        function downloadTestLogs() {
            if (window.logger) {
                window.logger.downloadLogs('txt');
                console.log('日志文件已下载');
            }
        }

        function clearTestLogs() {
            if (window.logger) {
                window.logger.clearLogs();
                document.getElementById('log-output').textContent = '';
                console.log('日志已清除');
            }
        }

        function testDataManager() {
            const result = document.getElementById('data-manager-result');
            try {
                const manager = new MarketDataManager({
                    apiBaseUrl: 'http://127.0.0.1:8001',
                    wsBaseUrl: 'ws://127.0.0.1:8001'
                });

                // 测试缓存功能
                const testData = [{ timestamp: Date.now(), close: 100 }];
                manager.setCachedData('BTCUSDT', '1h', testData);
                const cachedData = manager.getCachedData('BTCUSDT', '1h');

                if (cachedData && cachedData.length > 0) {
                    result.className = 'test-result success';
                    result.textContent = '✅ 数据管理器测试成功';
                    console.log('MarketDataManager test passed');
                } else {
                    throw new Error('缓存数据测试失败');
                }
            } catch (error) {
                result.className = 'test-result error';
                result.textContent = `❌ 数据管理器测试失败: ${error.message}`;
                console.error('MarketDataManager test failed:', error);
            }
        }

        // 页面加载完成后自动检查依赖
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(checkDependencies, 500);
        });
    </script>
</body>
</html>
