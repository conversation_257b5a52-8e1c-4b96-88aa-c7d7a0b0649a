{"version": 3, "file": "rfc2253Parser.js", "sourceRoot": "", "sources": ["../src/rfc2253Parser.ts"], "names": [], "mappings": ";;;AAAA,SAAgB,OAAO,CAAC,GAAW;IACjC,IAAI,MAAM,GAAG,KAAK,CAAA;IAClB,IAAI,GAAG,GAAkB,IAAI,CAAA;IAC7B,IAAI,KAAK,GAAG,EAAE,CAAA;IACd,IAAI,YAAY,GAAG,CAAC,CAAA;IAEpB,GAAG,GAAG,GAAG,CAAC,IAAI,EAAE,CAAA;IAChB,MAAM,MAAM,GAAG,IAAI,GAAG,EAAkB,CAAA;IACxC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACrC,IAAI,CAAC,KAAK,GAAG,CAAC,MAAM,EAAE,CAAC;YACrB,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;gBACjB,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;YACxB,CAAC;YACD,MAAK;QACP,CAAC;QAED,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAA;QACjB,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,EAAE,KAAK,GAAG,EAAE,CAAC;gBACf,MAAM,GAAG,KAAK,CAAA;gBACd,SAAQ;YACV,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,EAAE,KAAK,GAAG,EAAE,CAAC;gBACf,MAAM,GAAG,IAAI,CAAA;gBACb,SAAQ;YACV,CAAC;YAED,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC;gBAChB,CAAC,EAAE,CAAA;gBACH,MAAM,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;gBAC7C,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;oBACtB,KAAK,IAAI,GAAG,CAAC,CAAC,CAAC,CAAA;gBACjB,CAAC;qBAAM,CAAC;oBACN,CAAC,EAAE,CAAA;oBACH,KAAK,IAAI,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA;gBACnC,CAAC;gBACD,SAAQ;YACV,CAAC;YAED,IAAI,GAAG,KAAK,IAAI,IAAI,EAAE,KAAK,GAAG,EAAE,CAAC;gBAC/B,GAAG,GAAG,KAAK,CAAA;gBACX,KAAK,GAAG,EAAE,CAAA;gBACV,SAAQ;YACV,CAAC;YAED,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,KAAK,GAAG,EAAE,CAAC;gBAC3C,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;oBACjB,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;gBACxB,CAAC;gBACD,GAAG,GAAG,IAAI,CAAA;gBACV,KAAK,GAAG,EAAE,CAAA;gBACV,SAAQ;YACV,CAAC;QACH,CAAC;QAED,IAAI,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YAC1B,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvB,SAAQ;YACV,CAAC;YAED,IAAI,CAAC,GAAG,YAAY,EAAE,CAAC;gBACrB,IAAI,CAAC,GAAG,CAAC,CAAA;gBACT,OAAO,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;oBACtB,CAAC,EAAE,CAAA;gBACL,CAAC;gBACD,YAAY,GAAG,CAAC,CAAA;YAClB,CAAC;YAED,IACE,YAAY,IAAI,GAAG,CAAC,MAAM;gBAC1B,GAAG,CAAC,YAAY,CAAC,KAAK,GAAG;gBACzB,GAAG,CAAC,YAAY,CAAC,KAAK,GAAG;gBACzB,CAAC,GAAG,KAAK,IAAI,IAAI,GAAG,CAAC,YAAY,CAAC,KAAK,GAAG,CAAC;gBAC3C,CAAC,GAAG,KAAK,IAAI,IAAI,GAAG,CAAC,YAAY,CAAC,KAAK,GAAG,CAAC,EAC3C,CAAC;gBACD,CAAC,GAAG,YAAY,GAAG,CAAC,CAAA;gBACpB,SAAQ;YACV,CAAC;QACH,CAAC;QAED,KAAK,IAAI,EAAE,CAAA;IACb,CAAC;IAED,OAAO,MAAM,CAAA;AACf,CAAC;AArFD,0BAqFC", "sourcesContent": ["export function parseDn(seq: string): Map<string, string> {\n  let quoted = false\n  let key: string | null = null\n  let token = \"\"\n  let nextNonSpace = 0\n\n  seq = seq.trim()\n  const result = new Map<string, string>()\n  for (let i = 0; i <= seq.length; i++) {\n    if (i === seq.length) {\n      if (key !== null) {\n        result.set(key, token)\n      }\n      break\n    }\n\n    const ch = seq[i]\n    if (quoted) {\n      if (ch === '\"') {\n        quoted = false\n        continue\n      }\n    } else {\n      if (ch === '\"') {\n        quoted = true\n        continue\n      }\n\n      if (ch === \"\\\\\") {\n        i++\n        const ord = parseInt(seq.slice(i, i + 2), 16)\n        if (Number.isNaN(ord)) {\n          token += seq[i]\n        } else {\n          i++\n          token += String.fromCharCode(ord)\n        }\n        continue\n      }\n\n      if (key === null && ch === \"=\") {\n        key = token\n        token = \"\"\n        continue\n      }\n\n      if (ch === \",\" || ch === \";\" || ch === \"+\") {\n        if (key !== null) {\n          result.set(key, token)\n        }\n        key = null\n        token = \"\"\n        continue\n      }\n    }\n\n    if (ch === \" \" && !quoted) {\n      if (token.length === 0) {\n        continue\n      }\n\n      if (i > nextNonSpace) {\n        let j = i\n        while (seq[j] === \" \") {\n          j++\n        }\n        nextNonSpace = j\n      }\n\n      if (\n        nextNonSpace >= seq.length ||\n        seq[nextNonSpace] === \",\" ||\n        seq[nextNonSpace] === \";\" ||\n        (key === null && seq[nextNonSpace] === \"=\") ||\n        (key !== null && seq[nextNonSpace] === \"+\")\n      ) {\n        i = nextNonSpace - 1\n        continue\n      }\n    }\n\n    token += ch\n  }\n\n  return result\n}\n"]}