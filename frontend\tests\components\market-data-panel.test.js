/**
 * MarketDataPanel 单元测试
 */

TestUtils.testRunner.test('MarketDataPanel - 初始化', () => {
    const container = document.createElement('div');
    const panel = new MarketDataPanel(container);
    
    TestUtils.assert.exists(panel, 'MarketDataPanel should be created');
    TestUtils.assert.equals(panel.activeSymbol, 'BTCUSDT', 'Default active symbol should be BTCUSDT');
    TestUtils.assert.isArray(panel.options.symbols, 'Symbols should be an array');
    TestUtils.assert.equals(panel.options.symbols.length, 3, 'Should have 3 default symbols');
});

TestUtils.testRunner.test('MarketDataPanel - 数据初始化', () => {
    const container = document.createElement('div');
    const panel = new MarketDataPanel(container);
    
    // 检查数据映射是否正确初始化
    TestUtils.assert.equals(panel.symbolData.size, 3, 'Should have 3 symbol data entries');
    TestUtils.assert.equals(panel.previousData.size, 3, 'Should have 3 previous data entries');
    
    // 检查每个符号的数据结构
    const btcData = panel.symbolData.get('BTCUSDT');
    TestUtils.assert.exists(btcData, 'BTC data should exist');
    TestUtils.assert.equals(btcData.symbol, 'BTCUSDT', 'Symbol should be correct');
    TestUtils.assert.equals(btcData.price, 0, 'Initial price should be 0');
});

TestUtils.testRunner.test('MarketDataPanel - 更新符号数据', () => {
    const container = document.createElement('div');
    const panel = new MarketDataPanel(container);
    
    const testData = {
        price: 50000,
        change: 1000,
        changePercent: 2.5,
        volume: 1000000,
        high24h: 51000,
        low24h: 49000
    };
    
    let updateEventTriggered = false;
    panel.on('dataUpdated', (event) => {
        updateEventTriggered = true;
        TestUtils.assert.equals(event.symbol, 'BTCUSDT', 'Event should have correct symbol');
        TestUtils.assert.equals(event.data.price, 50000, 'Event should have correct price');
    });
    
    panel.updateSymbolData('BTCUSDT', testData);
    
    const updatedData = panel.symbolData.get('BTCUSDT');
    TestUtils.assert.equals(updatedData.price, 50000, 'Price should be updated');
    TestUtils.assert.equals(updatedData.changePercent, 2.5, 'Change percent should be updated');
    TestUtils.assert.isTrue(updateEventTriggered, 'dataUpdated event should be triggered');
});

TestUtils.testRunner.test('MarketDataPanel - 设置激活符号', () => {
    const container = document.createElement('div');
    const panel = new MarketDataPanel(container);
    
    let activeChangeEventTriggered = false;
    panel.on('activeSymbolChanged', (symbol) => {
        activeChangeEventTriggered = true;
        TestUtils.assert.equals(symbol, 'ETHUSDT', 'Event should have correct symbol');
    });
    
    panel.setActiveSymbol('ETHUSDT');
    
    TestUtils.assert.equals(panel.getActiveSymbol(), 'ETHUSDT', 'Active symbol should be updated');
    TestUtils.assert.isTrue(activeChangeEventTriggered, 'activeSymbolChanged event should be triggered');
});

TestUtils.testRunner.test('MarketDataPanel - 格式化符号名称', () => {
    const container = document.createElement('div');
    const panel = new MarketDataPanel(container);
    
    TestUtils.assert.equals(panel.formatSymbolName('BTCUSDT'), 'BTC/USDT', 'Should format BTC correctly');
    TestUtils.assert.equals(panel.formatSymbolName('ETHUSDT'), 'ETH/USDT', 'Should format ETH correctly');
    TestUtils.assert.equals(panel.formatSymbolName('SOLUSDT'), 'SOL/USDT', 'Should format SOL correctly');
    TestUtils.assert.equals(panel.formatSymbolName('CUSTOM'), 'CUSTOM', 'Should return original for non-USDT pairs');
});

TestUtils.testRunner.test('MarketDataPanel - 格式化价格', () => {
    const container = document.createElement('div');
    const panel = new MarketDataPanel(container);
    
    TestUtils.assert.equals(panel.formatPrice(0), '0.00', 'Should format zero price');
    TestUtils.assert.equals(panel.formatPrice(50000), '50,000.00', 'Should format large price with commas');
    TestUtils.assert.equals(panel.formatPrice(1.5), '1.50', 'Should format small price with 2 decimals');
    TestUtils.assert.equals(panel.formatPrice(0.0001), '0.0001', 'Should format very small price with 4 decimals');
});

TestUtils.testRunner.test('MarketDataPanel - 颜色方法', () => {
    const container = document.createElement('div');
    const panel = new MarketDataPanel(container, { theme: 'dark' });
    
    TestUtils.assert.exists(panel.getBackgroundColor(), 'Background color should exist');
    TestUtils.assert.exists(panel.getActiveCardColor(), 'Active card color should exist');
    TestUtils.assert.exists(panel.getHoverColor(), 'Hover color should exist');
    
    // 测试主题切换
    panel.options.theme = 'light';
    const lightBg = panel.getBackgroundColor();
    panel.options.theme = 'dark';
    const darkBg = panel.getBackgroundColor();
    
    TestUtils.assert.notEquals(lightBg, darkBg, 'Light and dark themes should have different colors');
});

TestUtils.testRunner.test('MarketDataPanel - 事件系统', () => {
    const container = document.createElement('div');
    const panel = new MarketDataPanel(container);
    
    let callCount = 0;
    const testCallback = () => {
        callCount++;
    };
    
    // 添加事件监听器
    panel.on('test', testCallback);
    
    // 触发事件
    panel.emit('test');
    TestUtils.assert.equals(callCount, 1, 'Event callback should be called once');
    
    // 移除事件监听器
    panel.off('test', testCallback);
    panel.emit('test');
    TestUtils.assert.equals(callCount, 1, 'Event callback should not be called after removal');
});

TestUtils.testRunner.test('MarketDataPanel - 错误处理', () => {
    // 测试无效容器
    TestUtils.assert.throws(() => {
        new MarketDataPanel(null);
    }, 'Should throw error for null container');
    
    const container = document.createElement('div');
    const panel = new MarketDataPanel(container);
    
    // 测试无效符号
    panel.updateSymbolData('INVALID', { price: 100 });
    // 应该不会抛出错误，但会记录警告
    
    // 测试无效激活符号
    panel.setActiveSymbol('INVALID');
    // 应该不会抛出错误，但会记录警告
    TestUtils.assert.equals(panel.getActiveSymbol(), 'BTCUSDT', 'Active symbol should remain unchanged for invalid symbol');
});

TestUtils.testRunner.test('MarketDataPanel - 销毁', () => {
    const container = document.createElement('div');
    const panel = new MarketDataPanel(container);
    
    let destroyEventTriggered = false;
    panel.on('destroyed', () => {
        destroyEventTriggered = true;
    });
    
    TestUtils.assert.doesNotThrow(() => {
        panel.destroy();
    }, 'destroy should not throw error');
    
    TestUtils.assert.isTrue(destroyEventTriggered, 'destroyed event should be triggered');
    TestUtils.assert.equals(panel.element, null, 'Element should be null after destroy');
    TestUtils.assert.equals(panel.symbolData.size, 0, 'Symbol data should be cleared after destroy');
    TestUtils.assert.equals(panel.previousData.size, 0, 'Previous data should be cleared after destroy');
});

TestUtils.testRunner.test('MarketDataPanel - 动画效果', () => {
    const container = document.createElement('div');
    document.body.appendChild(container); // 需要添加到DOM中才能测试动画
    const panel = new MarketDataPanel(container);
    
    // 设置初始数据
    panel.updateSymbolData('BTCUSDT', { price: 50000 });
    
    // 更新数据触发动画
    TestUtils.assert.doesNotThrow(() => {
        panel.updateSymbolData('BTCUSDT', { price: 51000 });
    }, 'Animation should not throw error');
    
    // 清理
    document.body.removeChild(container);
});

console.log('MarketDataPanel tests loaded');
