/**
 * 性能优化工具函数
 */

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间（毫秒）
 * @param {boolean} immediate - 是否立即执行
 * @returns {Function} 防抖后的函数
 */
function debounce(func, wait, immediate = false) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            timeout = null;
            if (!immediate) func.apply(this, args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func.apply(this, args);
    };
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} limit - 限制时间（毫秒）
 * @returns {Function} 节流后的函数
 */
function throttle(func, limit) {
    let inThrottle;
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * 请求动画帧节流
 * @param {Function} func - 要节流的函数
 * @returns {Function} 节流后的函数
 */
function rafThrottle(func) {
    let rafId = null;
    return function(...args) {
        if (rafId === null) {
            rafId = requestAnimationFrame(() => {
                func.apply(this, args);
                rafId = null;
            });
        }
    };
}

/**
 * LRU缓存实现
 */
class LRUCache {
    constructor(capacity = 100) {
        this.capacity = capacity;
        this.cache = new Map();
    }

    get(key) {
        if (this.cache.has(key)) {
            // 移动到最前面
            const value = this.cache.get(key);
            this.cache.delete(key);
            this.cache.set(key, value);
            return value;
        }
        return null;
    }

    set(key, value) {
        if (this.cache.has(key)) {
            // 更新现有值
            this.cache.delete(key);
        } else if (this.cache.size >= this.capacity) {
            // 删除最旧的项
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }
        this.cache.set(key, value);
    }

    has(key) {
        return this.cache.has(key);
    }

    delete(key) {
        return this.cache.delete(key);
    }

    clear() {
        this.cache.clear();
    }

    size() {
        return this.cache.size;
    }
}

/**
 * 内存监控器
 */
class MemoryMonitor {
    constructor(options = {}) {
        this.options = {
            checkInterval: 30000, // 30秒检查一次
            warningThreshold: 50 * 1024 * 1024, // 50MB警告阈值
            ...options
        };
        
        this.isMonitoring = false;
        this.intervalId = null;
        this.callbacks = new Set();
    }

    start() {
        if (this.isMonitoring) return;
        
        this.isMonitoring = true;
        this.intervalId = setInterval(() => {
            this.checkMemory();
        }, this.options.checkInterval);
        
        console.log('Memory monitoring started');
    }

    stop() {
        if (!this.isMonitoring) return;
        
        this.isMonitoring = false;
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
        
        console.log('Memory monitoring stopped');
    }

    checkMemory() {
        if (performance.memory) {
            const memory = performance.memory;
            const used = memory.usedJSHeapSize;
            const total = memory.totalJSHeapSize;
            const limit = memory.jsHeapSizeLimit;
            
            const memoryInfo = {
                used,
                total,
                limit,
                usedMB: (used / 1024 / 1024).toFixed(2),
                totalMB: (total / 1024 / 1024).toFixed(2),
                limitMB: (limit / 1024 / 1024).toFixed(2),
                usagePercent: ((used / limit) * 100).toFixed(2)
            };
            
            // 触发回调
            this.callbacks.forEach(callback => {
                try {
                    callback(memoryInfo);
                } catch (error) {
                    console.error('Memory monitor callback error:', error);
                }
            });
            
            // 检查警告阈值
            if (used > this.options.warningThreshold) {
                console.warn('Memory usage warning:', memoryInfo);
            }
            
            return memoryInfo;
        }
        
        return null;
    }

    onMemoryCheck(callback) {
        this.callbacks.add(callback);
    }

    offMemoryCheck(callback) {
        this.callbacks.delete(callback);
    }
}

/**
 * 错误边界处理器
 */
class ErrorBoundary {
    constructor(options = {}) {
        this.options = {
            maxErrors: 10,
            resetTime: 60000, // 1分钟后重置错误计数
            ...options
        };
        
        this.errorCount = 0;
        this.lastResetTime = Date.now();
        this.errorCallbacks = new Set();
        
        this.setupGlobalErrorHandlers();
    }

    setupGlobalErrorHandlers() {
        // 捕获JavaScript错误
        window.addEventListener('error', (event) => {
            this.handleError({
                type: 'javascript',
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error,
                timestamp: Date.now()
            });
        });

        // 捕获Promise拒绝
        window.addEventListener('unhandledrejection', (event) => {
            this.handleError({
                type: 'promise',
                message: event.reason?.message || 'Unhandled promise rejection',
                reason: event.reason,
                timestamp: Date.now()
            });
        });
    }

    handleError(errorInfo) {
        // 重置错误计数（如果超过重置时间）
        const now = Date.now();
        if (now - this.lastResetTime > this.options.resetTime) {
            this.errorCount = 0;
            this.lastResetTime = now;
        }

        this.errorCount++;
        
        console.error('Error boundary caught error:', errorInfo);
        
        // 触发错误回调
        this.errorCallbacks.forEach(callback => {
            try {
                callback(errorInfo);
            } catch (error) {
                console.error('Error in error callback:', error);
            }
        });

        // 检查是否超过最大错误数
        if (this.errorCount >= this.options.maxErrors) {
            console.error('Maximum error count reached, application may be unstable');
            this.triggerEmergencyMode();
        }
    }

    triggerEmergencyMode() {
        // 紧急模式：停止所有非关键功能
        console.warn('Entering emergency mode due to excessive errors');
        
        // 可以在这里添加紧急模式的逻辑
        // 比如停止WebSocket连接、清理缓存等
    }

    onError(callback) {
        this.errorCallbacks.add(callback);
    }

    offError(callback) {
        this.errorCallbacks.delete(callback);
    }

    getErrorCount() {
        return this.errorCount;
    }

    reset() {
        this.errorCount = 0;
        this.lastResetTime = Date.now();
    }
}

/**
 * 资源清理器
 */
class ResourceCleaner {
    constructor() {
        this.resources = new Set();
        this.cleanupCallbacks = new Map();
    }

    register(resource, cleanupCallback) {
        const id = this.generateId();
        this.resources.add(id);
        this.cleanupCallbacks.set(id, cleanupCallback);
        
        // 返回清理函数
        return () => {
            this.unregister(id);
        };
    }

    unregister(id) {
        if (this.resources.has(id)) {
            const callback = this.cleanupCallbacks.get(id);
            if (callback) {
                try {
                    callback();
                } catch (error) {
                    console.error('Error during resource cleanup:', error);
                }
            }
            
            this.resources.delete(id);
            this.cleanupCallbacks.delete(id);
        }
    }

    cleanupAll() {
        for (const id of this.resources) {
            this.unregister(id);
        }
    }

    generateId() {
        return `resource_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    getResourceCount() {
        return this.resources.size;
    }
}

/**
 * 性能监控器
 */
class PerformanceMonitor {
    constructor() {
        this.metrics = new Map();
        this.observers = new Set();
    }

    mark(name) {
        if (performance.mark) {
            performance.mark(name);
        }
        
        this.metrics.set(name, {
            type: 'mark',
            timestamp: performance.now(),
            name
        });
    }

    measure(name, startMark, endMark) {
        if (performance.measure) {
            performance.measure(name, startMark, endMark);
        }
        
        const startTime = this.metrics.get(startMark)?.timestamp || 0;
        const endTime = this.metrics.get(endMark)?.timestamp || performance.now();
        const duration = endTime - startTime;
        
        const measurement = {
            type: 'measure',
            name,
            duration,
            startMark,
            endMark,
            timestamp: performance.now()
        };
        
        this.metrics.set(name, measurement);
        
        // 通知观察者
        this.observers.forEach(observer => {
            try {
                observer(measurement);
            } catch (error) {
                console.error('Performance observer error:', error);
            }
        });
        
        return measurement;
    }

    getMetric(name) {
        return this.metrics.get(name);
    }

    getAllMetrics() {
        return Array.from(this.metrics.values());
    }

    clearMetrics() {
        this.metrics.clear();
        if (performance.clearMarks) {
            performance.clearMarks();
        }
        if (performance.clearMeasures) {
            performance.clearMeasures();
        }
    }

    observe(callback) {
        this.observers.add(callback);
        return () => this.observers.delete(callback);
    }
}

// 创建全局实例
const memoryMonitor = new MemoryMonitor();
const errorBoundary = new ErrorBoundary();
const resourceCleaner = new ResourceCleaner();
const performanceMonitor = new PerformanceMonitor();

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        debounce,
        throttle,
        rafThrottle,
        LRUCache,
        MemoryMonitor,
        ErrorBoundary,
        ResourceCleaner,
        PerformanceMonitor,
        memoryMonitor,
        errorBoundary,
        resourceCleaner,
        performanceMonitor
    };
} else {
    window.PerformanceUtils = {
        debounce,
        throttle,
        rafThrottle,
        LRUCache,
        MemoryMonitor,
        ErrorBoundary,
        ResourceCleaner,
        PerformanceMonitor,
        memoryMonitor,
        errorBoundary,
        resourceCleaner,
        performanceMonitor
    };
}
