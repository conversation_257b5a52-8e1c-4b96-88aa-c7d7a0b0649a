<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>加密货币量化交易K线图</title>
    <script src="./node_modules/klinecharts/dist/klinecharts.min.js"></script>
    <!-- 工具脚本 -->
    <script src="./src/utils/performance.js"></script>
    <script src="./src/utils/logger.js"></script>
    <!-- 组件脚本 -->
    <script src="./src/components/chart/ChartComponent.js"></script>
    <script src="./src/components/chart/TimeframeSelector.js"></script>
    <script src="./src/components/market/MarketDataPanel.js"></script>
    <script src="./src/managers/MarketDataManager.js"></script>
    <script src="./src/app.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #1a1a1a;
            color: #ffffff;
            overflow: hidden;
        }

        .app-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        /* 顶部工具栏 */
        .toolbar {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            padding: 10px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            z-index: 100;
        }

        .toolbar-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .toolbar-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .app-title {
            font-size: 18px;
            font-weight: bold;
            color: #ecf0f1;
        }

        .symbol-selector {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .symbol-selector label {
            font-size: 14px;
            color: #bdc3c7;
        }

        .symbol-selector select {
            background: #34495e;
            color: #ecf0f1;
            border: 1px solid #5d6d7e;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 14px;
        }

        .control-buttons {
            display: flex;
            gap: 10px;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background-color 0.3s;
        }

        .btn:hover {
            background: #2980b9;
        }

        .btn.danger {
            background: #e74c3c;
        }

        .btn.danger:hover {
            background: #c0392b;
        }

        .btn.success {
            background: #27ae60;
        }

        .btn.success:hover {
            background: #229954;
        }

        /* 主要内容区域 */
        .main-content {
            display: flex;
            flex: 1;
            overflow: hidden;
        }

        /* 侧边栏 */
        .sidebar {
            width: 250px;
            background: #2c3e50;
            border-right: 1px solid #34495e;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }

        .sidebar-section {
            padding: 15px;
            border-bottom: 1px solid #34495e;
        }

        .sidebar-section h3 {
            font-size: 14px;
            color: #ecf0f1;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* 市场数据面板样式 */
        #market-data-panel {
            margin-bottom: 16px;
        }

        .indicator-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .indicator-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px;
            background: #34495e;
            border-radius: 4px;
            font-size: 12px;
        }

        .indicator-value {
            color: #3498db;
            font-weight: bold;
        }

        .signal-indicator {
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .signal-buy {
            background: #27ae60;
            color: white;
        }

        .signal-sell {
            background: #e74c3c;
            color: white;
        }

        .signal-neutral {
            background: #95a5a6;
            color: white;
        }

        /* 图表容器 */
        .charts-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chart-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .main-chart-container {
            flex: 1;
            background: #1a1a1a;
            position: relative;
            border-radius: 8px;
            margin: 8px;
            overflow: hidden;
        }

        #main-chart {
            width: 100%;
            height: 100%;
            position: relative;
        }

        /* 状态栏 */
        .status-bar {
            background: #2c3e50;
            padding: 8px 20px;
            border-top: 1px solid #34495e;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
        }

        .status {
            color: #bdc3c7;
        }

        .status.error {
            color: #e74c3c;
        }

        .status.success {
            color: #27ae60;
        }

        .connection-status {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .connection-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #e74c3c;
        }

        .connection-dot.connected {
            background: #27ae60;
        }

        .status-left, .status-center, .status-right {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-center {
            flex: 1;
            justify-content: center;
        }

        .status.warn {
            color: #f39c12;
        }

        .status.info {
            color: #3498db;
        }

        .status-btn {
            background: #34495e;
            color: #ecf0f1;
            border: 1px solid #5d6d7e;
            padding: 4px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 11px;
            transition: background-color 0.3s;
        }

        .status-btn:hover {
            background: #3c5a78;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .sidebar {
                width: 200px;
            }
        }

        @media (max-width: 768px) {
            .chart-grid {
                grid-template-columns: 1fr;
                grid-template-rows: repeat(3, 1fr);
            }

            .sidebar {
                display: none;
            }
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #2c3e50;
        }

        ::-webkit-scrollbar-thumb {
            background: #5d6d7e;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #7f8c8d;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 顶部工具栏 -->
        <div class="toolbar">
            <div class="toolbar-left">
                <div class="app-title">🚀 加密货币量化交易分析</div>
                <div class="symbol-selector">
                    <label for="symbol-select">交易对:</label>
                    <select id="symbol-select" onchange="changeSymbol(this.value)">
                        <option value="BTCUSDT">BTC/USDT</option>
                        <option value="ETHUSDT">ETH/USDT</option>
                        <option value="BNBUSDT">BNB/USDT</option>
                        <option value="ADAUSDT">ADA/USDT</option>
                        <option value="SOLUSDT">SOL/USDT</option>
                    </select>
                </div>
            </div>
            <div class="toolbar-right">
                <div class="control-buttons">
                    <button class="btn success" onclick="refreshData()">🔄 刷新</button>
                    <button class="btn" onclick="syncCharts()">🔗 同步</button>
                    <button class="btn danger" onclick="clearCharts()">🗑️ 清除</button>
                </div>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <!-- 市场数据面板 -->
                <div id="market-data-panel"></div>

                <div class="sidebar-section">
                    <h3>📊 技术指标</h3>
                    <div class="indicator-list" id="indicator-list">
                        <div class="indicator-item">
                            <span>MA5</span>
                            <span class="indicator-value" id="ma5-value">--</span>
                        </div>
                        <div class="indicator-item">
                            <span>MA10</span>
                            <span class="indicator-value" id="ma10-value">--</span>
                        </div>
                        <div class="indicator-item">
                            <span>MA20</span>
                            <span class="indicator-value" id="ma20-value">--</span>
                        </div>
                        <div class="indicator-item">
                            <span>RSI</span>
                            <span class="indicator-value" id="rsi-value">--</span>
                        </div>
                        <div class="indicator-item">
                            <span>MACD</span>
                            <span class="indicator-value" id="macd-value">--</span>
                        </div>
                    </div>
                </div>

                <div class="sidebar-section">
                    <h3>📈 交易信号</h3>
                    <div class="indicator-list" id="signal-list">
                        <div class="indicator-item">
                            <span>趋势</span>
                            <span class="signal-indicator signal-neutral" id="trend-signal">中性</span>
                        </div>
                        <div class="indicator-item">
                            <span>信号</span>
                            <span class="signal-indicator signal-neutral" id="trade-signal">无</span>
                        </div>
                        <div class="indicator-item">
                            <span>强度</span>
                            <span class="indicator-value" id="signal-strength">0</span>
                        </div>
                    </div>
                </div>

                <div class="sidebar-section">
                    <h3>⚙️ 设置</h3>
                    <div class="indicator-list">
                        <div class="indicator-item">
                            <label for="ma-period">MA周期:</label>
                            <input type="number" id="ma-period" value="20" min="5" max="200" style="width: 60px; background: #34495e; color: white; border: 1px solid #5d6d7e; border-radius: 3px; padding: 2px;">
                        </div>
                        <div class="indicator-item">
                            <label for="rsi-period">RSI周期:</label>
                            <input type="number" id="rsi-period" value="14" min="5" max="50" style="width: 60px; background: #34495e; color: white; border: 1px solid #5d6d7e; border-radius: 3px; padding: 2px;">
                        </div>
                    </div>
                </div>
            </div>

            <!-- 图表区域 -->
            <div class="charts-container">
                <div class="chart-content">
                    <div class="main-chart-container">
                        <div id="main-chart"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span class="connection-status">
                    <span class="connection-dot" id="connection-dot"></span>
                    <span id="connection-text">断开</span>
                </span>
            </div>
            <div class="status-center">
                <span id="status" class="status">准备就绪</span>
            </div>
            <div class="status-right">
                <button onclick="refreshData()" class="status-btn">🔄 刷新</button>
                <button onclick="clearCharts()" class="status-btn">🗑️ 清除</button>
                <button onclick="downloadLogs()" class="status-btn">📥 日志</button>
                <button onclick="clearLogs()" class="status-btn">🧹 清理</button>
            </div>
        </div>
    </div>

    <script>
        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            try {
                console.log('Initializing new CryptoTradingApp...');

                // 设置日志UI回调
                if (window.logger) {
                    window.logger.onUILog((logEntry) => {
                        updateStatusDisplay(logEntry.message, logEntry.level.toLowerCase());
                    });
                }

                window.app = new CryptoTradingApp();

                // 设置全局错误处理
                window.app.on('error', (error) => {
                    console.error('App error:', error);
                });

                window.app.on('initialized', () => {
                    console.log('App initialized successfully');
                });

            } catch (error) {
                console.error('Failed to initialize app:', error);
            }
        });

        // 更新状态显示
        function updateStatusDisplay(message, type = 'info') {
            const statusElement = document.getElementById('status');
            if (statusElement) {
                const timestamp = new Date().toLocaleTimeString();
                statusElement.textContent = `[${timestamp}] ${message}`;
                statusElement.className = `status ${type}`;

                // 3秒后清除错误状态
                if (type === 'error') {
                    setTimeout(() => {
                        statusElement.className = 'status';
                    }, 3000);
                }
            }
        }

        // 下载日志文件
        function downloadLogs() {
            if (window.logger) {
                window.logger.downloadLogs('txt');
            }
        }

        // 清除日志
        function clearLogs() {
            if (window.logger) {
                window.logger.clearLogs();
                updateStatusDisplay('日志已清除', 'info');
            }
        }

        // 保持向后兼容的全局函数
        function changeSymbol(symbol) {
            if (window.app) {
                window.app.changeSymbol(symbol);
            }
        }

        function syncCharts() {
            // 新架构中不需要同步多个图表
            console.log('syncCharts called - not needed in single chart mode');
        }
    </script>
</body>
</html>