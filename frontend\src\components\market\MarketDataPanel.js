/**
 * 市场数据面板组件
 * 负责显示多个交易对的实时行情数据
 */
class MarketDataPanel {
    constructor(container, options = {}) {
        this.container = container;
        this.options = {
            symbols: ['BTCUSDT', 'ETHUSDT', 'SOLUSDT'],
            theme: 'dark',
            updateInterval: 1000,
            animationDuration: 300,
            ...options
        };
        
        this.symbolData = new Map();
        this.previousData = new Map();
        this.activeSymbol = this.options.symbols[0];
        this.eventListeners = new Map();
        this.element = null;
        this.updateTimer = null;
        
        this.initialize();
    }

    /**
     * 初始化组件
     */
    initialize() {
        try {
            if (!this.container) {
                throw new Error('Container is required');
            }

            // 初始化数据
            this.initializeData();
            
            // 渲染组件
            this.render();
            
            // 绑定事件
            this.bindEvents();
            
            console.log('MarketDataPanel initialized successfully');
            this.emit('initialized');
            
        } catch (error) {
            console.error('Failed to initialize MarketDataPanel:', error);
            this.emit('error', error);
        }
    }

    /**
     * 初始化数据
     */
    initializeData() {
        this.options.symbols.forEach(symbol => {
            this.symbolData.set(symbol, {
                symbol: symbol,
                price: 0,
                change: 0,
                changePercent: 0,
                volume: 0,
                high24h: 0,
                low24h: 0,
                lastUpdate: Date.now()
            });
            
            this.previousData.set(symbol, {
                price: 0,
                change: 0,
                changePercent: 0
            });
        });
    }

    /**
     * 渲染组件
     */
    render() {
        // 创建主容器
        this.element = document.createElement('div');
        this.element.className = `market-data-panel ${this.options.theme}`;
        this.element.style.cssText = this.getContainerStyles();

        // 创建标题
        const title = document.createElement('h3');
        title.className = 'panel-title';
        title.textContent = '实时行情';
        title.style.cssText = this.getTitleStyles();
        this.element.appendChild(title);

        // 创建符号卡片容器
        const cardsContainer = document.createElement('div');
        cardsContainer.className = 'symbol-cards';
        cardsContainer.style.cssText = this.getCardsContainerStyles();

        // 创建每个符号的卡片
        this.options.symbols.forEach(symbol => {
            const card = this.createSymbolCard(symbol);
            cardsContainer.appendChild(card);
        });

        this.element.appendChild(cardsContainer);
        this.container.appendChild(this.element);

        // 设置初始激活状态
        this.setActiveSymbol(this.activeSymbol);
    }

    /**
     * 创建符号卡片
     * @param {string} symbol - 交易对符号
     * @returns {HTMLElement} 卡片元素
     */
    createSymbolCard(symbol) {
        const card = document.createElement('div');
        card.className = 'symbol-card';
        card.dataset.symbol = symbol;
        card.style.cssText = this.getCardStyles();

        // 符号名称
        const symbolName = document.createElement('div');
        symbolName.className = 'symbol-name';
        symbolName.textContent = this.formatSymbolName(symbol);
        symbolName.style.cssText = this.getSymbolNameStyles();

        // 价格显示
        const priceDisplay = document.createElement('div');
        priceDisplay.className = 'price-display';
        priceDisplay.style.cssText = this.getPriceDisplayStyles();

        const price = document.createElement('span');
        price.className = 'price';
        price.textContent = '0.00';
        price.style.cssText = this.getPriceStyles();

        const change = document.createElement('span');
        change.className = 'change';
        change.textContent = '+0.00%';
        change.style.cssText = this.getChangeStyles();

        priceDisplay.appendChild(price);
        priceDisplay.appendChild(change);

        // 24小时统计
        const stats = document.createElement('div');
        stats.className = 'stats';
        stats.style.cssText = this.getStatsStyles();

        const high = document.createElement('span');
        high.className = 'high';
        high.textContent = 'H: 0.00';

        const low = document.createElement('span');
        low.className = 'low';
        low.textContent = 'L: 0.00';

        stats.appendChild(high);
        stats.appendChild(low);

        // 组装卡片
        card.appendChild(symbolName);
        card.appendChild(priceDisplay);
        card.appendChild(stats);

        // 添加点击效果
        this.addCardInteraction(card);

        return card;
    }

    /**
     * 添加卡片交互效果
     * @param {HTMLElement} card - 卡片元素
     */
    addCardInteraction(card) {
        // 悬停效果
        card.addEventListener('mouseenter', () => {
            if (!card.classList.contains('active')) {
                card.style.backgroundColor = this.getHoverColor();
                card.style.transform = 'translateY(-2px)';
            }
        });

        card.addEventListener('mouseleave', () => {
            if (!card.classList.contains('active')) {
                card.style.backgroundColor = this.getCardBackgroundColor();
                card.style.transform = 'translateY(0)';
            }
        });

        // 点击效果
        card.addEventListener('click', () => {
            const symbol = card.dataset.symbol;
            this.setActiveSymbol(symbol);
            this.emit('symbolClick', symbol);
        });
    }

    /**
     * 更新符号数据
     * @param {string} symbol - 交易对符号
     * @param {Object} data - 新数据
     */
    updateSymbolData(symbol, data) {
        try {
            if (!this.symbolData.has(symbol)) {
                console.warn(`Unknown symbol: ${symbol}`);
                return;
            }

            // 保存之前的数据用于比较
            const currentData = this.symbolData.get(symbol);
            this.previousData.set(symbol, {
                price: currentData.price,
                change: currentData.change,
                changePercent: currentData.changePercent
            });

            // 更新数据
            const updatedData = {
                ...currentData,
                ...data,
                lastUpdate: Date.now()
            };
            
            this.symbolData.set(symbol, updatedData);

            // 更新UI
            this.updateCardDisplay(symbol, updatedData);

            // 触发动画效果
            this.animateChange(symbol, currentData, updatedData);

            this.emit('dataUpdated', { symbol, data: updatedData });

        } catch (error) {
            console.error('Failed to update symbol data:', error);
            this.emit('error', error);
        }
    }

    /**
     * 更新卡片显示
     * @param {string} symbol - 交易对符号
     * @param {Object} data - 数据
     */
    updateCardDisplay(symbol, data) {
        const card = this.element.querySelector(`[data-symbol="${symbol}"]`);
        if (!card) return;

        // 更新价格
        const priceElement = card.querySelector('.price');
        if (priceElement) {
            priceElement.textContent = this.formatPrice(data.price);
        }

        // 更新涨跌幅
        const changeElement = card.querySelector('.change');
        if (changeElement) {
            const changeText = data.changePercent >= 0 ? 
                `+${data.changePercent.toFixed(2)}%` : 
                `${data.changePercent.toFixed(2)}%`;
            changeElement.textContent = changeText;
            
            // 设置颜色
            const color = data.changePercent >= 0 ? '#26A69A' : '#EF5350';
            changeElement.style.color = color;
        }

        // 更新24小时统计
        const highElement = card.querySelector('.high');
        const lowElement = card.querySelector('.low');
        
        if (highElement) {
            highElement.textContent = `H: ${this.formatPrice(data.high24h)}`;
        }
        
        if (lowElement) {
            lowElement.textContent = `L: ${this.formatPrice(data.low24h)}`;
        }
    }

    /**
     * 动画效果
     * @param {string} symbol - 交易对符号
     * @param {Object} oldData - 旧数据
     * @param {Object} newData - 新数据
     */
    animateChange(symbol, oldData, newData) {
        const card = this.element.querySelector(`[data-symbol="${symbol}"]`);
        if (!card) return;

        const priceElement = card.querySelector('.price');
        if (!priceElement) return;

        // 价格变化动画
        if (oldData.price !== newData.price) {
            const isIncrease = newData.price > oldData.price;
            const flashColor = isIncrease ? '#26A69A' : '#EF5350';
            
            // 添加闪烁效果
            priceElement.style.transition = 'none';
            priceElement.style.backgroundColor = flashColor;
            priceElement.style.color = '#ffffff';
            priceElement.style.borderRadius = '4px';
            priceElement.style.padding = '2px 4px';
            
            setTimeout(() => {
                priceElement.style.transition = `all ${this.options.animationDuration}ms ease`;
                priceElement.style.backgroundColor = 'transparent';
                priceElement.style.color = this.getPriceColor();
                priceElement.style.padding = '0';
            }, 150);
        }
    }

    /**
     * 设置激活的交易对
     * @param {string} symbol - 交易对符号
     */
    setActiveSymbol(symbol) {
        if (!this.options.symbols.includes(symbol)) {
            console.warn(`Invalid symbol: ${symbol}`);
            return;
        }

        // 移除所有卡片的激活状态
        const cards = this.element.querySelectorAll('.symbol-card');
        cards.forEach(card => {
            card.classList.remove('active');
            card.style.backgroundColor = this.getCardBackgroundColor();
            card.style.borderColor = this.getCardBorderColor();
            card.style.transform = 'translateY(0)';
        });

        // 设置新的激活卡片
        const activeCard = this.element.querySelector(`[data-symbol="${symbol}"]`);
        if (activeCard) {
            activeCard.classList.add('active');
            activeCard.style.backgroundColor = this.getActiveCardColor();
            activeCard.style.borderColor = this.getActiveBorderColor();
            activeCard.style.transform = 'translateY(-1px)';
        }

        this.activeSymbol = symbol;
        this.emit('activeSymbolChanged', symbol);
    }

    /**
     * 获取激活的交易对
     * @returns {string} 当前激活的交易对
     */
    getActiveSymbol() {
        return this.activeSymbol;
    }

    /**
     * 格式化符号名称
     * @param {string} symbol - 原始符号
     * @returns {string} 格式化后的符号
     */
    formatSymbolName(symbol) {
        // 将 BTCUSDT 转换为 BTC/USDT
        if (symbol.endsWith('USDT')) {
            const base = symbol.slice(0, -4);
            return `${base}/USDT`;
        }
        return symbol;
    }

    /**
     * 格式化价格
     * @param {number} price - 价格
     * @returns {string} 格式化后的价格
     */
    formatPrice(price) {
        if (price === 0) return '0.00';
        
        if (price >= 1000) {
            return price.toLocaleString('en-US', { 
                minimumFractionDigits: 2, 
                maximumFractionDigits: 2 
            });
        } else if (price >= 1) {
            return price.toFixed(2);
        } else {
            return price.toFixed(4);
        }
    }

    // 样式方法
    getContainerStyles() {
        return `
            background: ${this.getBackgroundColor()};
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            border: 1px solid ${this.getBorderColor()};
        `;
    }

    getTitleStyles() {
        return `
            font-size: 14px;
            color: ${this.getTitleColor()};
            margin: 0 0 12px 0;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 600;
        `;
    }

    getCardsContainerStyles() {
        return `
            display: flex;
            flex-direction: column;
            gap: 8px;
        `;
    }

    getCardStyles() {
        return `
            background: ${this.getCardBackgroundColor()};
            border: 1px solid ${this.getCardBorderColor()};
            border-radius: 6px;
            padding: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            user-select: none;
        `;
    }

    getSymbolNameStyles() {
        return `
            font-size: 12px;
            font-weight: 600;
            color: ${this.getSymbolNameColor()};
            margin-bottom: 4px;
        `;
    }

    getPriceDisplayStyles() {
        return `
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
        `;
    }

    getPriceStyles() {
        return `
            font-size: 14px;
            font-weight: 700;
            color: ${this.getPriceColor()};
        `;
    }

    getChangeStyles() {
        return `
            font-size: 12px;
            font-weight: 600;
        `;
    }

    getStatsStyles() {
        return `
            display: flex;
            justify-content: space-between;
            font-size: 10px;
            color: ${this.getStatsColor()};
        `;
    }

    // 颜色方法
    getBackgroundColor() {
        return this.options.theme === 'dark' ? '#2c3e50' : '#ffffff';
    }

    getBorderColor() {
        return this.options.theme === 'dark' ? '#34495e' : '#e0e0e0';
    }

    getTitleColor() {
        return this.options.theme === 'dark' ? '#ecf0f1' : '#2c3e50';
    }

    getCardBackgroundColor() {
        return this.options.theme === 'dark' ? '#34495e' : '#f8f9fa';
    }

    getCardBorderColor() {
        return this.options.theme === 'dark' ? '#5d6d7e' : '#dee2e6';
    }

    getActiveCardColor() {
        return this.options.theme === 'dark' ? '#3c5a78' : '#e3f2fd';
    }

    getActiveBorderColor() {
        return '#3498db';
    }

    getHoverColor() {
        return this.options.theme === 'dark' ? '#3c5a78' : '#f0f0f0';
    }

    getSymbolNameColor() {
        return this.options.theme === 'dark' ? '#bdc3c7' : '#6c757d';
    }

    getPriceColor() {
        return this.options.theme === 'dark' ? '#ecf0f1' : '#212529';
    }

    getStatsColor() {
        return this.options.theme === 'dark' ? '#95a5a6' : '#6c757d';
    }

    /**
     * 销毁组件
     */
    destroy() {
        try {
            if (this.updateTimer) {
                clearInterval(this.updateTimer);
                this.updateTimer = null;
            }
            
            if (this.element) {
                this.element.remove();
                this.element = null;
            }
            
            this.symbolData.clear();
            this.previousData.clear();
            this.eventListeners.clear();
            
            this.emit('destroyed');
            
        } catch (error) {
            console.error('Failed to destroy MarketDataPanel:', error);
        }
    }

    /**
     * 事件监听
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    /**
     * 移除事件监听
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    off(event, callback) {
        if (this.eventListeners.has(event)) {
            const listeners = this.eventListeners.get(event);
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }

    /**
     * 触发事件
     * @param {string} event - 事件名称
     * @param {*} data - 事件数据
     */
    emit(event, data) {
        if (this.eventListeners.has(event)) {
            this.eventListeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in event listener for ${event}:`, error);
                }
            });
        }
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 这里可以添加其他需要的事件绑定
    }
}

// 导出组件
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MarketDataPanel;
} else {
    window.MarketDataPanel = MarketDataPanel;
}
