/**
 * 日志管理器
 * 负责将日志输出到文件和控制台
 */
class Logger {
    constructor(options = {}) {
        this.options = {
            level: 'info', // debug, info, warn, error
            maxLogSize: 1024 * 1024, // 1MB
            maxLogFiles: 5,
            enableConsole: true,
            enableFile: true,
            enableUI: true,
            ...options
        };
        
        this.logs = [];
        this.logLevels = {
            debug: 0,
            info: 1,
            warn: 2,
            error: 3
        };
        
        this.currentLevel = this.logLevels[this.options.level] || 1;
        this.uiCallbacks = new Set();
        
        // 拦截原生console方法
        this.interceptConsole();
        
        // 定期清理日志
        this.startLogCleanup();
    }

    /**
     * 拦截原生console方法
     */
    interceptConsole() {
        const originalConsole = {
            log: console.log,
            info: console.info,
            warn: console.warn,
            error: console.error,
            debug: console.debug
        };

        console.log = (...args) => {
            this.log('info', args.join(' '));
            if (this.options.enableConsole) {
                originalConsole.log.apply(console, args);
            }
        };

        console.info = (...args) => {
            this.log('info', args.join(' '));
            if (this.options.enableConsole) {
                originalConsole.info.apply(console, args);
            }
        };

        console.warn = (...args) => {
            this.log('warn', args.join(' '));
            if (this.options.enableConsole) {
                originalConsole.warn.apply(console, args);
            }
        };

        console.error = (...args) => {
            this.log('error', args.join(' '));
            if (this.options.enableConsole) {
                originalConsole.error.apply(console, args);
            }
        };

        console.debug = (...args) => {
            this.log('debug', args.join(' '));
            if (this.options.enableConsole) {
                originalConsole.debug.apply(console, args);
            }
        };

        // 保存原始方法的引用
        this.originalConsole = originalConsole;
    }

    /**
     * 记录日志
     * @param {string} level - 日志级别
     * @param {string} message - 日志消息
     * @param {Object} meta - 元数据
     */
    log(level, message, meta = {}) {
        const levelNum = this.logLevels[level] || 1;
        
        // 检查日志级别
        if (levelNum < this.currentLevel) {
            return;
        }

        const logEntry = {
            timestamp: new Date().toISOString(),
            level: level.toUpperCase(),
            message,
            meta,
            id: this.generateLogId()
        };

        // 添加到内存日志
        this.logs.push(logEntry);

        // 限制内存中的日志数量
        if (this.logs.length > 1000) {
            this.logs = this.logs.slice(-500);
        }

        // 输出到文件
        if (this.options.enableFile) {
            this.writeToFile(logEntry);
        }

        // 输出到UI
        if (this.options.enableUI) {
            this.notifyUI(logEntry);
        }
    }

    /**
     * 写入文件
     * @param {Object} logEntry - 日志条目
     */
    writeToFile(logEntry) {
        try {
            // 在浏览器环境中，我们使用localStorage来模拟文件存储
            const logKey = `crypto_ui_logs_${new Date().toISOString().split('T')[0]}`;
            const existingLogs = localStorage.getItem(logKey) || '';
            const logLine = this.formatLogEntry(logEntry);
            
            const newLogs = existingLogs + logLine + '\n';
            
            // 检查大小限制
            if (newLogs.length > this.options.maxLogSize) {
                // 保留最后一半的日志
                const lines = newLogs.split('\n');
                const keepLines = lines.slice(-Math.floor(lines.length / 2));
                localStorage.setItem(logKey, keepLines.join('\n'));
            } else {
                localStorage.setItem(logKey, newLogs);
            }
            
        } catch (error) {
            // 如果localStorage满了，清理旧日志
            this.cleanupOldLogs();
        }
    }

    /**
     * 格式化日志条目
     * @param {Object} logEntry - 日志条目
     * @returns {string} 格式化的日志字符串
     */
    formatLogEntry(logEntry) {
        const timestamp = new Date(logEntry.timestamp).toLocaleString();
        const metaStr = Object.keys(logEntry.meta).length > 0 ? 
            ` | ${JSON.stringify(logEntry.meta)}` : '';
        
        return `[${timestamp}] [${logEntry.level}] ${logEntry.message}${metaStr}`;
    }

    /**
     * 通知UI更新
     * @param {Object} logEntry - 日志条目
     */
    notifyUI(logEntry) {
        this.uiCallbacks.forEach(callback => {
            try {
                callback(logEntry);
            } catch (error) {
                this.originalConsole.error('Error in UI log callback:', error);
            }
        });
    }

    /**
     * 添加UI回调
     * @param {Function} callback - 回调函数
     */
    onUILog(callback) {
        this.uiCallbacks.add(callback);
        return () => this.uiCallbacks.delete(callback);
    }

    /**
     * 生成日志ID
     * @returns {string} 日志ID
     */
    generateLogId() {
        return `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 获取日志
     * @param {Object} filters - 过滤条件
     * @returns {Array} 日志数组
     */
    getLogs(filters = {}) {
        let filteredLogs = [...this.logs];

        if (filters.level) {
            const levelNum = this.logLevels[filters.level];
            filteredLogs = filteredLogs.filter(log => 
                this.logLevels[log.level.toLowerCase()] >= levelNum
            );
        }

        if (filters.startTime) {
            filteredLogs = filteredLogs.filter(log => 
                new Date(log.timestamp) >= new Date(filters.startTime)
            );
        }

        if (filters.endTime) {
            filteredLogs = filteredLogs.filter(log => 
                new Date(log.timestamp) <= new Date(filters.endTime)
            );
        }

        if (filters.search) {
            const searchTerm = filters.search.toLowerCase();
            filteredLogs = filteredLogs.filter(log => 
                log.message.toLowerCase().includes(searchTerm)
            );
        }

        return filteredLogs;
    }

    /**
     * 导出日志
     * @param {string} format - 导出格式 (json, csv, txt)
     * @returns {string} 导出的日志内容
     */
    exportLogs(format = 'txt') {
        const logs = this.getLogs();
        
        switch (format) {
            case 'json':
                return JSON.stringify(logs, null, 2);
                
            case 'csv':
                const headers = 'Timestamp,Level,Message,Meta\n';
                const rows = logs.map(log => 
                    `"${log.timestamp}","${log.level}","${log.message}","${JSON.stringify(log.meta)}"`
                ).join('\n');
                return headers + rows;
                
            case 'txt':
            default:
                return logs.map(log => this.formatLogEntry(log)).join('\n');
        }
    }

    /**
     * 下载日志文件
     * @param {string} format - 文件格式
     */
    downloadLogs(format = 'txt') {
        const content = this.exportLogs(format);
        const filename = `crypto_ui_logs_${new Date().toISOString().split('T')[0]}.${format}`;
        
        const blob = new Blob([content], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    /**
     * 清理旧日志
     */
    cleanupOldLogs() {
        try {
            const keys = Object.keys(localStorage);
            const logKeys = keys.filter(key => key.startsWith('crypto_ui_logs_'));
            
            // 按日期排序
            logKeys.sort();
            
            // 保留最新的几个文件
            if (logKeys.length > this.options.maxLogFiles) {
                const toDelete = logKeys.slice(0, logKeys.length - this.options.maxLogFiles);
                toDelete.forEach(key => localStorage.removeItem(key));
            }
        } catch (error) {
            this.originalConsole.error('Failed to cleanup old logs:', error);
        }
    }

    /**
     * 开始日志清理定时器
     */
    startLogCleanup() {
        // 每小时清理一次
        setInterval(() => {
            this.cleanupOldLogs();
        }, 60 * 60 * 1000);
    }

    /**
     * 清除所有日志
     */
    clearLogs() {
        this.logs = [];
        
        // 清除localStorage中的日志
        const keys = Object.keys(localStorage);
        const logKeys = keys.filter(key => key.startsWith('crypto_ui_logs_'));
        logKeys.forEach(key => localStorage.removeItem(key));
    }

    /**
     * 设置日志级别
     * @param {string} level - 新的日志级别
     */
    setLevel(level) {
        if (this.logLevels.hasOwnProperty(level)) {
            this.options.level = level;
            this.currentLevel = this.logLevels[level];
        }
    }

    /**
     * 获取当前日志级别
     * @returns {string} 当前日志级别
     */
    getLevel() {
        return this.options.level;
    }

    /**
     * 销毁日志管理器
     */
    destroy() {
        // 恢复原始console方法
        if (this.originalConsole) {
            console.log = this.originalConsole.log;
            console.info = this.originalConsole.info;
            console.warn = this.originalConsole.warn;
            console.error = this.originalConsole.error;
            console.debug = this.originalConsole.debug;
        }
        
        // 清理回调
        this.uiCallbacks.clear();
    }
}

// 创建全局日志实例
const logger = new Logger({
    level: 'info',
    enableConsole: true,
    enableFile: true,
    enableUI: true
});

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { Logger, logger };
} else {
    window.Logger = Logger;
    window.logger = logger;
}
